{"data_mtime": 1754047861, "dep_lines": [17, 18, 19, 20, 21, 22, 23, 24, 25, 28, 29, 32, 35, 36, 19, 37, 12, 13, 14, 15, 38, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 10, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["traitlets.config.loader", "traitlets.config.application", "IPython.core.release", "IPython.core.usage", "IPython.core.completer", "IPython.core.crashhandler", "IPython.core.formatters", "IPython.core.history", "IPython.core.application", "IPython.core.magic", "IPython.core.magics", "IPython.core.shellapp", "IPython.extensions.storemagic", "IPython.terminal.interactiveshell", "IPython.core", "IPython.paths", "logging", "os", "sys", "warnings", "traitlets", "builtins", "IPython.core.interactiveshell", "_frozen_importlib", "_typeshed", "_warnings", "abc", "traitlets.config", "traitlets.config.configurable", "traitlets.traitlets", "traitlets.utils", "traitlets.utils.sentinel", "typing"], "hash": "f6206020d28a5e65e2b5efc59d2be2156ef80018", "id": "IPython.terminal.ipapp", "ignore_all": true, "interface_hash": "c8830ed9f2f1f4ef1d36f976e975255972c8db4c", "mtime": 1722816433, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\IPython\\terminal\\ipapp.py", "plugin_data": null, "size": 12407, "suppressed": [], "version_id": "1.17.1"}
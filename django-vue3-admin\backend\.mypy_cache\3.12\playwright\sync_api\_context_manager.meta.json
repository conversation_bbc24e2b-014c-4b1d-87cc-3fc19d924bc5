{"data_mtime": 1754047861, "dep_lines": [20, 21, 22, 23, 24, 25, 26, 29, 15, 16, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 18], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 25, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["playwright._impl._connection", "playwright._impl._errors", "playwright._impl._greenlets", "playwright._impl._object_factory", "playwright._impl._playwright", "playwright._impl._transport", "playwright.sync_api._generated", "asyncio.unix_events", "asyncio", "typing", "builtins", "_asyncio", "_frozen_importlib", "abc", "asyncio.events", "asyncio.tasks", "playwright._impl", "playwright._impl._impl_to_api_mapping", "playwright._impl._local_utils", "playwright._impl._sync_base", "pyee", "pyee.asyncio", "pyee.base"], "hash": "0fee63e267670bd1839a5ab5941c831498714574", "id": "playwright.sync_api._context_manager", "ignore_all": true, "interface_hash": "55ca83b2f68485309e9b8ffb44c8cdc157c85297", "mtime": 1722410462, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\playwright\\sync_api\\_context_manager.py", "plugin_data": null, "size": 3643, "suppressed": ["greenlet"], "version_id": "1.17.1"}
{"data_mtime": 1754047858, "dep_lines": [4, 23, 24, 27, 28, 30, 31, 32, 36, 2, 4, 5, 6, 7, 21, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 5, 25, 5, 20, 10, 10, 5, 10, 5, 30, 30, 30, 30], "dependencies": ["collections.abc", "_pytest._code", "_pytest.compat", "_pytest.config", "_pytest.deprecated", "_pytest.outcomes", "_pytest.scope", "_pytest.warning_types", "_pytest.nodes", "__future__", "collections", "dataclasses", "inspect", "typing", "warnings", "builtins", "_frozen_importlib", "abc", "enum", "types"], "hash": "384e5ecfffaa6f0ca4962f97f1192c829d03fde1", "id": "_pytest.mark.structures", "ignore_all": true, "interface_hash": "8b5c4aedecb29ef72d2609ce78fe0f0a6fcc8b61", "mtime": 1721810337, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\_pytest\\mark\\structures.py", "plugin_data": null, "size": 21039, "suppressed": [], "version_id": "1.17.1"}
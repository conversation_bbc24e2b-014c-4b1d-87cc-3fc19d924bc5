from django.urls import path
from . import template_views, api_views

app_name = 'website'

urlpatterns = [
    # 模板展示页面
    path('sites/', template_views.site_list, name='site_list'),
    path('sites/<int:site_id>/', template_views.site_detail, name='site_detail'),
    path('sites/<int:site_id>/column-rules/', template_views.column_rules_list, name='column_rules_list'),
    path('sites/<int:site_id>/columns/', template_views.site_columns_list, name='site_columns_list'),
    path('sitemaps/', template_views.sitemap_list, name='sitemap_list'),
    path('sitemaps/<int:sitemap_id>/', template_views.sitemap_detail, name='sitemap_detail'),
    
    # API接口 - 基础功能
    path('sitemaps/<int:sitemap_id>/crawl/', api_views.CrawlSitemapContentView.as_view(), name='crawl_sitemap_content'),
    
    # API接口 - 基础设施
    path('step/create_site/', api_views.CreateSiteView.as_view(), name='create_site'),
    path('step/generate_rules_to_sitemap/', api_views.GenerateRulesToSitemapView.as_view(), name='generate_rules_to_sitemap'),
    path('step/update_content_type_by_ai/', api_views.UpdateContentTypeByAIView.as_view(), name='update_content_type_by_ai'),
    
    # API接口 - 栏目相关
    path('step/generate_column_rule/<int:sitemap_id>/', api_views.GenerateColumnRuleView.as_view(), name='generate_column_rule'),
    path('step/associate_column_rules/', api_views.AssociateColumnRulesView.as_view(), name='associate_column_rules'),
    path('step/crawl_column_init/', api_views.CrawlColumnInitView.as_view(), name='crawl_column_init'),
    path('step/crawl_column_update/', api_views.CrawlColumnUpdateView.as_view(), name='crawl_column_update'),
    path('step/crawl_column_content/<int:sitemap_id>/', api_views.CrawlColumnContentView.as_view(), name='crawl_column_content'),
    path('step/update_column_content/<int:sitemap_id>/', api_views.UpdateColumnContentView.as_view(), name='update_column_content'),
    
    # API接口 - 单个URL处理
    path('step/update_single_url_content_type/<int:sitemap_id>/', api_views.UpdateSingleUrlContentTypeView.as_view(), name='update_single_url_content_type'),
    
    # API接口 - 规则详情
    path('api/column_rule_detail/<int:rule_id>/', api_views.ColumnRuleDetailView.as_view(), name='column_rule_detail'),
]



{"data_mtime": 1754047857, "dep_lines": [7, 14, 81, 3, 4, 5, 80, 1, 1, 1, 1], "dep_prios": [5, 5, 20, 10, 10, 10, 20, 5, 30, 30, 30], "dependencies": ["pyparsing.util", "pyparsing.unicode", "pyparsing.core", "re", "sys", "typing", "inspect", "builtins", "_frozen_importlib", "abc", "enum"], "hash": "725d9aeaf8c729e3bbe7b66d25dd40df85c71f66", "id": "pyparsing.exceptions", "ignore_all": true, "interface_hash": "5c3f9b034108d86189aa44ddcad5b6a556a5abfc", "mtime": 1740447914, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\pyparsing\\exceptions.py", "plugin_data": null, "size": 9503, "suppressed": [], "version_id": "1.17.1"}
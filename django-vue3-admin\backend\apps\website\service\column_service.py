#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
栏目服务
专注于网站栏目的管理和操作

重新设计的架构：
- ColumnBrowserPaginationHandler: 负责浏览器翻页操作
- ColumnPageExtractor: 负责内容项提取
- ColumnService: 负责流程协调和结果汇总
"""

import logging
import time
import traceback
from typing import Dict, Any, Optional, List, Tuple
from django.utils import timezone
from pyparsing import Enum

from apps.website.model.column import Column
from apps.website.models import SiteMap, ColumnRule, Site
from apps.website.service.site_map_service import SiteMapService
from apps.website.service.content_type_matcher import ContentTypeMatcher
from apps.website.service.column_rule_manager import ColumnRuleManager
from apps.website.service.column_browser_pagination_handler import ColumnBrowserPaginationHandler
from apps.website.service.column_page_extractor import ColumnPageExtractor
from apps.website.beans import ColumnConfigBean, ColumnContentListItem
from apps.website.enums import WebsiteContentType, CrawlMode
from django.db import transaction

logger = logging.getLogger(__name__)

class ColumnService:
    """栏目服务 - 流程协调器"""

    def __init__(self):
        """初始化服务"""
        self.site_map_service = SiteMapService()

    def get_or_create_column(self, site_map: SiteMap) -> Column:
        """
        根据SiteMap获取或创建对应的Column记录

        Args:
            site_map: SiteMap对象

        Returns:
            Column: Column对象
        """
        # 尝试根据unique_id查找现有Column
        column = Column.objects.filter(unique_id=site_map.unique_id).first()
        if column:
            logger.debug(f"找到现有Column: {column.name}")
            return column

        # 如果不存在，创建新的Column
        column = self._create_column_from_sitemap(site_map)
        logger.info(f"创建新Column: {column.name}")
        return column

    def _create_column_from_sitemap(self, site_map: SiteMap) -> Column:
        """
        从SiteMap创建新的Column记录

        Args:
            site_map: SiteMap对象

        Returns:
            Column: 创建的Column对象
        """
        with transaction.atomic():
            column = Column.objects.create(
                unique_id=site_map.unique_id,
                site_id=site_map.site_id,
                name=site_map.title or "未命名栏目",
                description=f"从SiteMap自动创建的栏目，URL: {site_map.url}",
                status='active',
                max_crawl_pages=10,
                crawl_frequency='weekly'
            )

            logger.info(f"从SiteMap创建Column成功: {column.name}")
            return column

    def _get_default_result_dict(self, sitemap: SiteMap) -> Dict[str, Any]:
        """获取默认的结果字典"""
        return {
            'success': False,
            'sitemap_url': sitemap.url,
            'sitemap_title': sitemap.title or '无标题',
            'pages_crawled': 0,
            'content_items_found': 0,
            'content_items_saved': 0,
            'content_items_new': 0,
            'content_items_updated': 0,
            'pagination_rule_used': None,
            'error_message': None,
            'processing_time': 0.0
        }

    def crawl_colum(self, column: Column, max_pages: int = 10,mode:CrawlMode=CrawlMode.DEEP_CRAWL) -> Dict[str, Any]:
        """
        抓取栏目内容 - 新架构实现

        使用新的分页翻页机制：
        1. 获取分页配置
        2. 初始化ColumnBrowserPaginationHandler进行翻页
        3. 初始化ColumnPageExtractor进行内容提取
        4. 逐页抓取和提取内容
        5. 保存内容项到数据库

        Args:
            sitemap: 栏目对应的SiteMap对象
            max_pages: 最大抓取页数，默认10页
            mode: 抓取模式，默认深度抓取、补充更新

        Returns:
            Dict[str, Any]: 抓取结果统计
        """
        sitemap=column.get_sitemap()
        if sitemap is None:
            logger.error(f"栏目 {column.name} 没有对应的SiteMap")
            raise ValueError(f"栏目 {column.name} 没有对应的SiteMap")
        start_time = time.time()
        
        result = self._get_default_result_dict(sitemap)
        pagination_handler = None

        try:
            logger.info(f"开始抓取栏目: {sitemap.url}")
            logger.info(f"栏目标题: {sitemap.title}")
            logger.info(f"最大页数: {max_pages}")

            # 第1步：获取站点和栏目规则
            column_rule = column.get_column_rule()
            if column_rule is None:
                raise ValueError(f"栏目 {column.name} 没有对应的栏目规则")

            result['pagination_rule_used'] = column_rule.name
            logger.info(f"使用栏目规则: {column_rule.name}")

            # 第2步：初始化分页处理器和内容提取器
            column_config_bean = column_rule.get_column_config_bean()

            # 使用静态工厂方法创建处理器
            pagination_handler = ColumnBrowserPaginationHandler.createHandler(
                url=sitemap.url,
                column_config=column_config_bean
            )

            # 第3步：打开栏目页面
            pagination_handler.launch()

            site = sitemap.site
            # 第4步：逐页抓取内容
            crawl_stats, error_message = self._crawl_pages_with_new_architecture(
                pagination_handler, column_config_bean, site, max_pages, column, mode
            )

            # 更新结果统计
            result.update(crawl_stats)
            result['success'] = error_message is None
            result['error_message'] = error_message

            # 更新SiteMap的最后抓取时间
            if result['success']:
                sitemap.last_crawl_time = timezone.now()
                sitemap.save(update_fields=['last_crawl_time'])

            logger.info(f"栏目抓取完成: {sitemap.url}")
            logger.info(f"成功: {result['success']}, 页数: {result['pages_crawled']}, 内容项: {result['content_items_found']}")

        except Exception as e:
            result['error_message'] = f"栏目抓取异常: {str(e)}"
            traceback.print_exc()
            logger.error(result['error_message'], exc_info=True)

        finally:
            # 清理浏览器资源
            if pagination_handler:
                pagination_handler.close()

            result['processing_time'] = time.time() - start_time

        return result

    def _get_column_rule(self, sitemap: SiteMap) -> Optional[ColumnRule]:
        """获取栏目规则"""
        try:
            # 使用ColumnPaginationRuleManager获取栏目规则
            return ColumnRuleManager.get_column_rule(sitemap)
        except Exception as e:
            logger.error(f"获取栏目规则失败: {str(e)}")
            return None

    def _crawl_pages_with_new_architecture(self, pagination_handler: ColumnBrowserPaginationHandler,
                                         column_config_bean: ColumnConfigBean,
                                         site: Site, max_pages: int,
                                         column: Column, mode: CrawlMode) -> Tuple[Dict[str, int], Optional[str]]:
        """使用新架构进行分页抓取，并支持不同抓取模式"""
        stats = {
            'pages_crawled': 0,
            'content_items_found': 0,
            'content_items_saved': 0,
            'content_items_new': 0,
            'content_items_updated': 0,
        }
        all_crawled_pages = []
        previous_page_urls = set()  # 记录上一页的内容URL，用于重复检测

        try:
            content_matcher = ContentTypeMatcher(site.url_matching_rules)
            
            # 根据抓取模式，决定起始页
            start_page = self._get_start_page_for_mode(pagination_handler, column, mode)
            if start_page > 1:
                # 跳转到指定页面
                pagination_handler.navigate_to_page(start_page)

            current_page = start_page

            # 处理第一页
            html_content = pagination_handler.get_page_html()
            if not html_content:
                return stats, "无法获取第一页HTML内容"

            # 提取内容项
            content_items = ColumnPageExtractor.extract_content(
                html_content=html_content,
                column_config_bean=column_config_bean,
                page_url=pagination_handler.get_current_url()
            )

            page_stats = {'total': 0, 'saved': 0, 'new': 0, 'updated': 0}
            if not content_items:
                logger.warning("第一页未提取到内容项")
            else:
                logger.info(f"第一页: 找到 {len(content_items)} 个内容项")
                # 记录第一页的URL用于后续重复检测
                current_page_urls = {item.url for item in content_items}
                previous_page_urls = current_page_urls.copy()
                
                # 直接处理内容项Bean对象
                page_stats = self._process_content_items_new(site, content_items, content_matcher, column)
                # 累计统计
                stats['content_items_found'] += page_stats['total']
                stats['content_items_saved'] += page_stats['saved']
                stats['content_items_new'] += page_stats['new']
                stats['content_items_updated'] += page_stats['updated']

            stats['pages_crawled'] = 1
            all_crawled_pages.append(current_page)

            # 增量更新模式下，如果第一页没有新内容，可以提前结束
            if self._is_update_crawl_finished(page_stats, mode):
                logger.info(f"增量抓取模式：在第 {current_page} 页发现无新内容，抓取结束。")
                self._update_crawled_pages(column, all_crawled_pages)
                return stats, None


            # 如果有多页，继续处理后续页面
            while pagination_handler.current_page_info and current_page < max_pages:
                # 检查是否为最后一页
                next_page_selector = pagination_handler.column_config.pagination_config.next_page if pagination_handler.column_config and pagination_handler.column_config.pagination_config else None
                if pagination_handler.current_page_info.is_end(pagination_handler.page, next_page_selector):
                    logger.info("已到达最后一页，停止抓取")
                    break
                current_page += 1
                logger.info(f"处理第 {current_page} 页")

                # 执行翻页
                page_pagination_info = pagination_handler.next_page()
                if not page_pagination_info:
                    logger.warning(f"无法导航到第 {current_page} 页")
                    break

                # 获取页面HTML内容
                html_content = pagination_handler.get_page_html()
                if not html_content:
                    logger.warning(f"第 {current_page} 页HTML内容获取失败")
                    break

                # 提取内容项
                content_items = ColumnPageExtractor.extract_content(
                    html_content=html_content,
                    column_config_bean=column_config_bean,
                    page_url=pagination_handler.get_current_url()
                )

                if not content_items:
                    logger.warning(f"第 {current_page} 页未提取到内容项")
                    continue

                logger.info(f"第 {current_page} 页: 找到 {len(content_items)} 个内容项")

                # 检查内容重复（用于识别伪装的下一页按钮）
                current_page_urls = {item.url for item in content_items}
                if current_page_urls == previous_page_urls:
                    logger.info(f"第 {current_page} 页内容与上一页重复，判定为最后一页")
                    break
                    
                # 更新上一页URL记录
                previous_page_urls = current_page_urls.copy()

                # 直接处理内容项Bean对象
                page_stats = self._process_content_items_new(site, content_items, content_matcher, column)

                # 累计统计
                stats['content_items_found'] += page_stats['total']
                stats['content_items_saved'] += page_stats['saved']
                stats['content_items_new'] += page_stats['new']
                stats['content_items_updated'] += page_stats['updated']

                stats['pages_crawled'] = current_page
                all_crawled_pages.append(current_page)
                
                # 增量模式下判断是否提前结束
                if self._is_update_crawl_finished(page_stats, mode):
                    logger.info(f"增量抓取模式：在第 {current_page} 页发现无新内容，抓取结束。")
                    break

                # 添加延迟避免请求过快
                time.sleep(1)
            
            logger.info(f"分页抓取完成: {stats['pages_crawled']}页, {stats['content_items_found']}个内容项")
            self._update_crawled_pages(column, all_crawled_pages)
            return stats, None

        except Exception as e:
            error_message = f"分页抓取异常: {str(e)}"
            logger.error(error_message, exc_info=True)
            return stats, error_message

    def _process_content_items_new(self, site: Site, content_items: List[ColumnContentListItem], content_matcher: ContentTypeMatcher, column: Column = None) -> Dict[str, int]:
        """处理内容项列表 - 新架构版本"""
        stats = {'total': 0, 'saved': 0, 'new': 0, 'updated': 0}

        for item in content_items:
            stats['total'] += 1

            try:
                item_url = item.url
                item_title = item.title

                if not item_url:
                    continue

                # 使用内容类型匹配器判断类型
                content_type = content_matcher.match_content_type(item_url)

                # 如果是content类型，则保存到数据库
                if content_type == WebsiteContentType.CONTENT:
                    sitemap, sitemap_created = self.site_map_service.create_or_update_sitemap_by_url(
                        site=site,
                        url=item_url,
                        title=item_title
                    )

                    # 更新内容类型
                    if sitemap_created or not sitemap.content_type_rule:
                        sitemap.content_type_rule = content_type
                        sitemap.save(update_fields=['content_type_rule'])

                    # 创建或更新Content记录
                    from apps.website.models import Content
                    content, content_created = Content.objects.get_or_create(
                        unique_id=sitemap.unique_id,
                        defaults={
                            'site_id': site.id,
                            'column_id': column.id if column else None,
                            'title': item_title or '无标题',
                            'status': 'pending'
                        }
                    )
                    
                    # 如果Content记录已存在但没有column_id，更新它
                    if not content_created and column and not content.column_id:
                        content.column_id = column.id
                        content.save(update_fields=['column_id'])

                    if sitemap_created or content_created:
                        stats['new'] += 1
                        logger.debug(f"新增内容: {item_title[:50]}... (Column: {column.name if column else 'N/A'})")
                    else:
                        stats['updated'] += 1
                        logger.debug(f"更新内容: {item_title[:50]}... (Column: {column.name if column else 'N/A'})")

                    stats['saved'] += 1

            except Exception as e:
                logger.error(f"处理内容项失败 {item}: {str(e)}")
                continue

        return stats
    def _get_start_page_for_mode(self, pagination_handler: ColumnBrowserPaginationHandler, column: Column, mode: CrawlMode) -> int:
        """
        根据抓取模式和历史记录获取起始页码
        
        Args:
            pagination_handler: 分页处理器
            column: 栏目对象
            mode: 抓取模式
        
        Returns:
            int: 起始页码
        """
        # 如果是增量抓取，始终从第一页开始
        if mode == CrawlMode.UPDATE_CRAWL:
            logger.info("增量抓取模式：从第1页开始抓取")
            return 1
            
        # 如果是深度抓取，且有已抓取记录，从最后记录的下一页开始
        if mode == CrawlMode.DEEP_CRAWL and column.crawled_pages:
            try:
                # 获取已抓取页码列表
                crawled_pages = column.crawled_pages
                if not crawled_pages:
                    logger.info("深度抓取模式：无历史记录，从第1页开始")
                    return 1
                
                # 找到最大页码
                max_page = max(int(page) for page in crawled_pages)
                next_page = max_page + 1
                
                # 检查页码是否在可用范围内
                if pagination_handler.current_page_info:
                    available_pages = pagination_handler.current_page_info.pages
                    if available_pages and str(next_page) in available_pages:
                        logger.info(f"深度抓取模式：从第{next_page}页继续抓取")
                        return next_page
                
                logger.info(f"深度抓取模式：计算得到的下一页{next_page}不在可用范围内，从第1页开始")
                return 1
            except (ValueError, TypeError) as e:
                logger.warning(f"解析已抓取页码失败: {str(e)}，从第1页开始")
                return 1
        
        # 默认从第一页开始
        logger.info("默认从第1页开始抓取")
        return 1
        
    def _is_update_crawl_finished(self, page_stats: Dict[str, int], mode: CrawlMode) -> bool:
        """
        判断增量抓取是否可以提前结束
        
        Args:
            page_stats: 当前页的抓取统计
            mode: 抓取模式
            
        Returns:
            bool: 如果可以结束，返回True
        """
        # 如果不是增量抓取模式，直接返回False
        if mode != CrawlMode.UPDATE_CRAWL:
            return False
        
        # 检查新增内容数量
        new_items_count = page_stats.get('new', 0)
        total_items_count = page_stats.get('total', 0)
        
        # 如果当前页没有内容，不应该结束（可能是临时错误）
        if total_items_count == 0:
            return False
            
        # 计算新内容比例
        if total_items_count > 0:
            new_ratio = new_items_count / total_items_count
            
            # 如果新内容比例低于阈值（例如20%），则可以结束
            # 这意味着大部分内容都是已经抓取过的
            if new_ratio < 0.2:
                logger.info(f"增量抓取可以结束：新内容比例 {new_ratio:.2f} < 0.2 (新增:{new_items_count}, 总数:{total_items_count})")
                return True
        
        # 如果没有新内容，可以结束
        if new_items_count == 0 and total_items_count > 0:
            logger.info(f"增量抓取可以结束：无新内容 (新增:{new_items_count}, 总数:{total_items_count})")
            return True
            
        # 默认继续抓取
        return False
        
    def _update_crawled_pages(self, column: Column, crawled_pages_list: List[int]):
        """
        更新栏目已抓取页码记录
        
        Args:
            column: 栏目对象
            crawled_pages_list: 本次抓取的所有页码
        """
        try:
            # 获取已有的crawled_pages
            existing_pages = column.crawled_pages or []
            
            # 将新的页码合并进去
            all_pages = existing_pages.copy()
            for page in crawled_pages_list:
                page_str = str(page)
                if page_str not in all_pages:
                    all_pages.append(page_str)
            
            # 将页码转换为整数进行排序
            sorted_pages = sorted([int(page) for page in all_pages])
            
            # 转回字符串列表
            column.crawled_pages = [str(page) for page in sorted_pages]
            
            # 保存回数据库
            column.save(update_fields=['crawled_pages'])
            
            logger.info(f"更新已抓取页码成功: {column.crawled_pages}")
        except Exception as e:
            logger.error(f"更新已抓取页码失败: {str(e)}")
            # 异常不应影响主流程，所以这里只记录日志，不抛出异常
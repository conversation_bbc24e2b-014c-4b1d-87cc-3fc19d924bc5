{"data_mtime": 1754047857, "dep_lines": [3, 5, 6, 10, 11, 12, 14, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["numpy.random._generator", "numpy.random._mt19937", "numpy.random._pcg64", "numpy.random._philox", "numpy.random._sfc64", "numpy.random.bit_generator", "numpy.random.mtrand", "numpy._pytesttester", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "990f45e50f04399db5cc7b5e6c0133da765873c1", "id": "numpy.random", "ignore_all": true, "interface_hash": "58818cd463b98d41e6a9ec8c5a04669852234173", "mtime": 1723175596, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\numpy\\random\\__init__.pyi", "plugin_data": null, "size": 2215, "suppressed": [], "version_id": "1.17.1"}
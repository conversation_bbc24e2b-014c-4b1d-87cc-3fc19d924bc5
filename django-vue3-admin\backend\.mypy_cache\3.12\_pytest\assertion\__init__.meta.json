{"data_mtime": 1754047858, "dep_lines": [11, 12, 13, 17, 15, 18, 22, 4, 6, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 5, 25, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest.assertion.rewrite", "_pytest.assertion.truncate", "_pytest.assertion.util", "_pytest.config.argparsing", "_pytest.config", "_pytest.nodes", "_pytest.main", "__future__", "sys", "typing", "builtins", "_frozen_importlib", "abc", "importlib", "importlib._abc", "importlib.abc", "pluggy", "pluggy._hooks", "pluggy._tracing"], "hash": "20e5600c6a5ed6287ae27383a78931266f5bb7f9", "id": "_pytest.assertion", "ignore_all": true, "interface_hash": "0dcbdbd541d2a1c9c8472e96cc04c1ab7f6f7ca2", "mtime": 1721810337, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\_pytest\\assertion\\__init__.py", "plugin_data": null, "size": 6791, "suppressed": [], "version_id": "1.17.1"}
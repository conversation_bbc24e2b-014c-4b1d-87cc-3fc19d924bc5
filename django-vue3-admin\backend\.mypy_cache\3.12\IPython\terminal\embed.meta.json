{"data_mtime": 1754047861, "dep_lines": [12, 12, 13, 14, 15, 16, 17, 20, 12, 9, 10, 19, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 5, 5, 20, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["IPython.core.ultratb", "IPython.core.compilerop", "IPython.core.magic_arguments", "IPython.core.magic", "IPython.core.interactiveshell", "IPython.terminal.interactiveshell", "IPython.terminal.ipapp", "IPython.utils.io", "IPython.core", "sys", "warnings", "traitlets", "typing", "builtins", "IPython.utils", "IPython.utils.colorable", "_collections_abc", "_frozen_importlib", "_typeshed", "_warnings", "abc", "traitlets.config", "traitlets.config.configurable", "traitlets.traitlets", "traitlets.utils", "traitlets.utils.sentinel", "types"], "hash": "0df47b3e203d85f7ad7689a758d77d1992a61234", "id": "IPython.terminal.embed", "ignore_all": true, "interface_hash": "a13ab77037900f626896ce9459b98bc4d00c7600", "mtime": 1722816433, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\IPython\\terminal\\embed.py", "plugin_data": null, "size": 16041, "suppressed": [], "version_id": "1.17.1"}
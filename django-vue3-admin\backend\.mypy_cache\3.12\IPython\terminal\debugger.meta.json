{"data_mtime": 1754047861, "dep_lines": [5, 6, 7, 8, 9, 13, 9, 14, 15, 16, 17, 1, 2, 3, 11, 19, 59, 169, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 12], "dep_prios": [5, 5, 5, 5, 10, 5, 20, 5, 5, 5, 5, 10, 10, 10, 5, 5, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["IPython.core.debugger", "IPython.core.completer", "IPython.terminal.ptutils", "IPython.terminal.shortcuts", "IPython.terminal.embed", "prompt_toolkit.shortcuts.prompt", "IPython.terminal", "prompt_toolkit.enums", "prompt_toolkit.formatted_text", "prompt_toolkit.history", "concurrent.futures", "asyncio", "os", "sys", "pathlib", "prompt_toolkit", "types", "pdb", "builtins", "IPython.core", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "asyncio.events", "bdb", "cmd", "concurrent", "concurrent.futures._base", "concurrent.futures.thread", "enum", "posixpath", "prompt_toolkit.auto_suggest", "prompt_toolkit.clipboard", "prompt_toolkit.clipboard.base", "prompt_toolkit.completion", "prompt_toolkit.completion.base", "prompt_toolkit.cursor_shapes", "prompt_toolkit.filters", "prompt_toolkit.filters.base", "prompt_toolkit.formatted_text.base", "prompt_toolkit.formatted_text.pygments", "prompt_toolkit.input", "prompt_toolkit.input.base", "prompt_toolkit.key_binding", "prompt_toolkit.key_binding.key_bindings", "prompt_toolkit.layout", "prompt_toolkit.layout.processors", "prompt_toolkit.lexers", "prompt_toolkit.lexers.base", "prompt_toolkit.mouse_events", "prompt_toolkit.output", "prompt_toolkit.output.base", "prompt_toolkit.output.color_depth", "prompt_toolkit.shortcuts", "prompt_toolkit.styles", "prompt_toolkit.styles.base", "prompt_toolkit.styles.style_transformation", "prompt_toolkit.validation", "traitlets", "traitlets.config", "traitlets.config.configurable", "traitlets.traitlets", "typing"], "hash": "08129725b256a38d042876215f023ba685817f89", "id": "IPython.terminal.debugger", "ignore_all": true, "interface_hash": "1454b4b489103625492af9db213070856cb5b6d0", "mtime": 1722816433, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\IPython\\terminal\\debugger.py", "plugin_data": null, "size": 6617, "suppressed": ["pygments.token"], "version_id": "1.17.1"}
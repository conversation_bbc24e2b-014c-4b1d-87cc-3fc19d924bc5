{"data_mtime": 1754047848, "dep_lines": [2, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 30, 30, 30, 5], "dependencies": ["uuid", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "e4b8089b6de4dda8bb1b2dbd853bd9de1c9375a5", "id": "apps.website.model.dify_call_log", "ignore_all": false, "interface_hash": "52877f4eae39cf92eb0f403cc7283ea46613bda1", "mtime": 1753151449, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\Project\\node\\crawl_all\\django-vue3-admin\\backend\\apps\\website\\model\\dify_call_log.py", "plugin_data": null, "size": 2473, "suppressed": ["django.db"], "version_id": "1.17.1"}
{"data_mtime": 1754047860, "dep_lines": [34, 40, 41, 42, 43, 48, 49, 50, 51, 52, 53, 54, 55, 72, 73, 74, 75, 76, 77, 80, 15, 16, 17, 18, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["playwright._impl._api_structures", "playwright._impl._artifact", "playwright._impl._cdp_session", "playwright._impl._clock", "playwright._impl._connection", "playwright._impl._console_message", "playwright._impl._dialog", "playwright._impl._errors", "playwright._impl._event_context_manager", "playwright._impl._fetch", "playwright._impl._frame", "playwright._impl._har_router", "playwright._impl._helper", "playwright._impl._network", "playwright._impl._page", "playwright._impl._str_utils", "playwright._impl._tracing", "playwright._impl._waiter", "playwright._impl._web_error", "playwright._impl._browser", "asyncio", "json", "pathlib", "types", "typing", "builtins", "_asyncio", "_contextvars", "_frozen_importlib", "abc", "asyncio.events", "asyncio.tasks", "os", "pyee", "pyee.asyncio", "pyee.base", "re"], "hash": "5c2c2eef298d3422a536de17922707f4db981661", "id": "playwright._impl._browser_context", "ignore_all": true, "interface_hash": "8b31fe32002e5d18640afd479fb82e0ebbfc737d", "mtime": 1722410462, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\playwright\\_impl\\_browser_context.py", "plugin_data": null, "size": 25374, "suppressed": [], "version_id": "1.17.1"}
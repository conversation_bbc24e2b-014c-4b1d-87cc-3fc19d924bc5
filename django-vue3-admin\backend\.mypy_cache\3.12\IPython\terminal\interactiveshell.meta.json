{"data_mtime": 1754047861, "dep_lines": [60, 61, 9, 10, 11, 12, 13, 36, 41, 48, 49, 50, 51, 52, 53, 31, 32, 33, 34, 35, 37, 38, 39, 40, 940, 3, 4, 5, 6, 7, 14, 42, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 44, 45, 46, 833], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 10, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5, 20], "dependencies": ["IPython.terminal.shortcuts.filters", "IPython.terminal.shortcuts.auto_suggest", "IPython.core.async_helpers", "IPython.core.interactiveshell", "IPython.utils.py3compat", "IPython.utils.terminal", "IPython.utils.process", "prompt_toolkit.layout.processors", "prompt_toolkit.styles.pygments", "IPython.terminal.debugger", "IPython.terminal.magics", "IPython.terminal.pt_inputhooks", "IPython.terminal.prompts", "IPython.terminal.ptutils", "IPython.terminal.shortcuts", "prompt_toolkit.auto_suggest", "prompt_toolkit.enums", "prompt_toolkit.filters", "prompt_toolkit.formatted_text", "prompt_toolkit.history", "prompt_toolkit.output", "prompt_toolkit.patch_stdout", "prompt_toolkit.shortcuts", "prompt_toolkit.styles", "prompt_toolkit.eventloop", "asyncio", "os", "sys", "warnings", "typing", "traitlets", "prompt_toolkit", "builtins", "IPython.core", "_frozen_importlib", "_typeshed", "abc", "asyncio.events", "contextlib", "enum", "prompt_toolkit.clipboard", "prompt_toolkit.clipboard.base", "prompt_toolkit.completion", "prompt_toolkit.completion.base", "prompt_toolkit.cursor_shapes", "prompt_toolkit.eventloop.inputhook", "prompt_toolkit.filters.base", "prompt_toolkit.formatted_text.base", "prompt_toolkit.input", "prompt_toolkit.input.base", "prompt_toolkit.key_binding", "prompt_toolkit.key_binding.key_bindings", "prompt_toolkit.layout", "prompt_toolkit.lexers", "prompt_toolkit.lexers.base", "prompt_toolkit.mouse_events", "prompt_toolkit.output.base", "prompt_toolkit.output.color_depth", "prompt_toolkit.shortcuts.prompt", "prompt_toolkit.styles.base", "prompt_toolkit.styles.style_transformation", "prompt_toolkit.validation", "traitlets.config", "traitlets.config.configurable", "traitlets.traitlets", "traitlets.utils", "traitlets.utils.sentinel", "types"], "hash": "0c7ec5758a491d03187d147ef942f028b30678d6", "id": "IPython.terminal.interactiveshell", "ignore_all": true, "interface_hash": "3a47de83fde36af74c58fef98d6ed25cc8a47207", "mtime": 1722816433, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\IPython\\terminal\\interactiveshell.py", "plugin_data": null, "size": 36812, "suppressed": ["pygments.styles", "pygments.style", "pygments.token", "colorama"], "version_id": "1.17.1"}
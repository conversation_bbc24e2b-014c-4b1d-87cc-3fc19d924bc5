{"data_mtime": 1754047858, "dep_lines": [42, 59, 66, 39, 40, 44, 45, 56, 60, 63, 64, 67, 70, 72, 82, 2, 4, 5, 7, 8, 9, 10, 11, 12, 13, 14, 36, 38, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 5, 10, 5, 10, 10, 10, 10, 5, 10, 10, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest.config.argparsing", "_pytest.mark.structures", "_pytest.nodes", "_pytest._code", "_pytest._io", "_pytest.compat", "_pytest.config", "_pytest.deprecated", "_pytest.main", "_pytest.mark", "_pytest.outcomes", "_pytest.pathlib", "_pytest.scope", "_pytest.python", "__future__", "abc", "collections", "dataclasses", "functools", "inspect", "os", "pathlib", "sys", "types", "typing", "warnings", "_pytest", "builtins", "_collections_abc", "_frozen_importlib", "_pytest._io.terminalwriter", "_pytest.warning_types", "_typeshed", "_warnings", "enum", "pluggy", "pluggy._manager"], "hash": "1d5ebd48f13e83e2973b44558d884208cf61edbc", "id": "_pytest.fixtures", "ignore_all": true, "interface_hash": "a9216d61fba6ba50108bc72255a4afadc7c83abd", "mtime": 1721810337, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\_pytest\\fixtures.py", "plugin_data": null, "size": 73108, "suppressed": [], "version_id": "1.17.1"}
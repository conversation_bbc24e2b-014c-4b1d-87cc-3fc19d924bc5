{"data_mtime": 1754047861, "dep_lines": [107, 110, 111, 112, 113, 114, 115, 116, 107, 110, 92, 93, 94, 95, 96, 97, 98, 99, 101, 103, 107, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 104, 105], "dep_prios": [10, 10, 5, 5, 10, 10, 10, 5, 20, 20, 10, 10, 10, 10, 10, 10, 10, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["IPython.utils.colorable", "IPython.core.debugger", "IPython.core.display_trap", "IPython.core.excolors", "IPython.utils.PyColorize", "IPython.utils.path", "IPython.utils.py3compat", "IPython.utils.terminal", "IPython.utils", "IPython.core", "functools", "inspect", "linecache", "pydoc", "sys", "time", "traceback", "types", "typing", "stack_data", "IPython", "builtins", "IPython.core.getipython", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "bdb", "cmd", "pdb", "traitlets", "traitlets.config", "traitlets.config.configurable", "traitlets.traitlets"], "hash": "d23daa304b96dd76f3b1f56abf4d9b2446f570c2", "id": "IPython.core.ultratb", "ignore_all": true, "interface_hash": "8e18cf61367c8a4e1c0ddc5871e4294766063056", "mtime": 1722816433, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\IPython\\core\\ultratb.py", "plugin_data": null, "size": 54536, "suppressed": ["pygments.formatters.terminal256", "pygments.styles"], "version_id": "1.17.1"}
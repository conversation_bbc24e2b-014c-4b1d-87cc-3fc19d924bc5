{"data_mtime": 1754047857, "dep_lines": [23, 24, 25, 26, 21, 10, 20, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["numpy._typing._shape", "numpy._typing._scalars", "numpy._typing._array_like", "numpy._typing._dtype_like", "numpy.typing", "typing", "numpy", "builtins", "_collections_abc", "_frozen_importlib", "abc", "numpy._typing._nested_sequence", "types"], "hash": "3c6496fcea7d003e36ef65eff97f3b4e676548c9", "id": "numpy._typing._ufunc", "ignore_all": true, "interface_hash": "f08387dc437fa4f95352d916b59dc1479d3e9279", "mtime": 1723175596, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\numpy\\_typing\\_ufunc.pyi", "plugin_data": null, "size": 13083, "suppressed": [], "version_id": "1.17.1"}
{% extends 'base.html' %}

{% block title %}{{ site.name }} - 站点详情{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'website:site_list' %}">站点列表</a></li>
        <li class="breadcrumb-item active">{{ site.name }}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<!-- 站点信息卡片 -->
<div class="card mb-4">
    <div class="card-header">
        <i class="fas fa-server"></i> 站点信息
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <h4>{{ site.name }}</h4>
                <p class="text-muted">{{ site.description|default:"无描述" }}</p>
                
                <div class="row mt-3">
                    <div class="col-md-6">
                        <strong>域名：</strong>
                        <a href="{{ site.start_url }}" target="_blank" class="text-decoration-none">
                            {{ site.domain }}
                            <i class="fas fa-external-link-alt ms-1"></i>
                        </a>
                    </div>
                    <div class="col-md-6">
                        <strong>状态：</strong>
                        {% if site.is_active %}
                            <span class="badge bg-success">活跃</span>
                        {% else %}
                            <span class="badge bg-secondary">停用</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mt-2">
                    <div class="col-md-6">
                        <strong>最大抓取页数：</strong> {{ site.max_crawl_pages }}
                    </div>
                    <div class="col-md-6">
                        <strong>创建时间：</strong> {{ site.created_at|date:"Y-m-d H:i" }}
                    </div>
                </div>
                
                {% if site.parent_site %}
                    <div class="row mt-2">
                        <div class="col-md-12">
                            <strong>父站点：</strong>
                            <a href="{% url 'website:site_detail' site.parent_site.id %}">
                                {{ site.parent_site.name }}
                            </a>
                        </div>
                    </div>
                {% endif %}
                
                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-info btn-sm" 
                                    onclick="toggleUrlRules()">
                                <i class="fas fa-eye"></i> 查看URL匹配规则 
                                ({{ site.url_matching_rules|length }} 条)
                            </button>
                            <a href="{% url 'website:column_rules_list' site.id %}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-cogs"></i> 栏目规则管理
                            </a>
                            <a href="{% url 'website:site_columns_list' site.id %}" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-list"></i> 查看栏目
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card">
                    <h3>{{ total_sitemaps }}</h3>
                    <p><i class="fas fa-sitemap"></i> 总页面数</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 批量AI判定操作面板 -->
<div class="card mb-4">
    <div class="card-header">
        <i class="fas fa-robot"></i> 批量AI内容类型判定
    </div>
    <div class="card-body">
        <div class="row align-items-end">
            <div class="col-md-3">
                <label for="batch-limit" class="form-label">判定数量</label>
                <input type="number" class="form-control" id="batch-limit" value="20" min="1" max="100" 
                       placeholder="输入要判定的数量">
                <small class="form-text text-muted">最多100个</small>
            </div>
            <div class="col-md-6">
                <label class="form-label">筛选条件</label>
                <div class="text-muted">
                    <small>
                        <i class="fas fa-info-circle"></i> 
                        将筛选出未判定的URL（除了"内容"类型），然后进行批量AI分析
                    </small>
                </div>
            </div>
            <div class="col-md-3">
                <button type="button" id="batch-ai-analyze-btn" class="btn btn-success w-100">
                    <i class="fas fa-robot"></i> 开始批量判定
                </button>
            </div>
        </div>
        
        <!-- 进度条和状态显示 -->
        <div id="batch-progress-container" class="mt-3" style="display: none;">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="fw-bold">批量处理进度</span>
                <span id="batch-progress-text">0/0</span>
            </div>
            <div class="progress mb-2">
                <div id="batch-progress-bar" class="progress-bar bg-success" role="progressbar" 
                     style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
            <div id="batch-status" class="text-muted">
                <small>准备开始...</small>
            </div>
        </div>
        
        <!-- 结果统计 -->
        <div id="batch-results-container" class="mt-3" style="display: none;">
            <div class="row">
                <div class="col-md-4">
                    <div class="text-center">
                        <h5 class="text-success mb-1" id="success-count">0</h5>
                        <small class="text-muted">成功判定</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <h5 class="text-danger mb-1" id="failed-count">0</h5>
                        <small class="text-muted">失败</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <h5 class="text-info mb-1" id="total-count">0</h5>
                        <small class="text-muted">总计</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- URL匹配规则显示面板 -->
<div id="url-rules-panel" class="card mb-4" style="display: none;">
    <div class="card-header">
        <i class="fas fa-code"></i> URL匹配规则
        <button type="button" class="btn btn-sm btn-outline-light ms-2" onclick="toggleUrlRules()">
            <i class="fas fa-times"></i> 隐藏
        </button>
    </div>
    <div class="card-body">
        {% if site.url_matching_rules %}
            <div class="table-responsive">
                <table class="table table-sm table-hover">
                    <thead>
                        <tr>
                            <th style="width: 80px;">序号</th>
                            <th>正则表达式</th>
                            <th style="width: 120px;">内容类型</th>
                            <th style="width: 100px;">测试</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for rule in site.url_matching_rules %}
                        <tr>
                            <td>{{ forloop.counter }}</td>
                            <td>
                                <code class="text-primary">{{ rule.pattern }}</code>
                            </td>
                            <td>
                                <span class="content-type-badge content-type-{{ rule.content_type }}">
                                    {{ rule.content_type }}
                                </span>
                            </td>
                            <td>
                                <button type="button" class="btn btn-sm btn-outline-secondary test-rule-btn"
                                        data-pattern="{{ rule.pattern }}"
                                        data-content-type="{{ rule.content_type }}"
                                        title="测试规则">
                                    <i class="fas fa-vial"></i> 测试
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- 规则测试区域 -->
            <div id="rule-test-area" class="mt-3" style="display: none;">
                <div class="row">
                    <div class="col-md-8">
                        <label for="test-url" class="form-label">测试URL：</label>
                        <input type="text" class="form-control" id="test-url" 
                               placeholder="输入要测试的URL">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="button" class="btn btn-primary" onclick="testAllRules()">
                                <i class="fas fa-play"></i> 测试所有规则
                            </button>
                        </div>
                    </div>
                </div>
                <div id="test-results" class="mt-3"></div>
            </div>
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-exclamation-triangle fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无URL匹配规则</h5>
                <p class="text-muted">请先运行站点初始化流程生成规则</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- 内容类型统计 -->
{% if content_type_stats %}
<div class="card mb-4">
    <div class="card-header">
        <i class="fas fa-chart-pie"></i> 内容类型统计
    </div>
    <div class="card-body">
        <div class="row">
            {% for type_key, type_info in content_type_stats.items %}
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="content-type-badge content-type-{{ type_key }}">
                        <strong>{{ type_info.label }}</strong>
                        <span class="badge bg-light text-dark ms-2">{{ type_info.count }}</span>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

<!-- 搜索和筛选 -->
<div class="search-form">
    <form method="get" class="row g-3">
        <div class="col-md-4">
            <label for="search" class="form-label">搜索页面</label>
            <input type="text" class="form-control" id="search" name="search" 
                   value="{{ search_query }}" placeholder="URL或标题关键词">
        </div>
        <div class="col-md-4">
            <label for="content_type" class="form-label">内容类型</label>
            <select class="form-select" id="content_type" name="content_type">
                <option value="">全部类型</option>
                {% for choice in content_type_choices %}
                    <option value="{{ choice.0 }}" {% if choice.0 == content_type %}selected{% endif %}>
                        {{ choice.1 }}
                    </option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-4">
            <label class="form-label">&nbsp;</label>
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> 搜索
                </button>
                <a href="{% url 'website:site_detail' site.id %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i> 清除
                </a>
            </div>
        </div>
    </form>
</div>

<!-- 页面列表 -->
<div class="card">
    <div class="card-header">
        <i class="fas fa-sitemap"></i> 页面列表
        {% if search_query or content_type %}
            <span class="badge bg-secondary ms-2">已筛选</span>
        {% endif %}
        <span class="badge bg-info ms-2">{{ sitemaps.paginator.count }} 个页面</span>
    </div>
    <div class="card-body p-0">
        {% if sitemaps %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th style="width: 50px;">ID</th>
                            <th style="width: 35%;">URL</th>
                            <th style="width: 25%;">标题</th>
                            <th style="width: 100px;">AI判定类型</th>
                            <th style="width: 90px;">创建时间</th>
                            <th style="width: 90px;">最后抓取</th>
                            <th style="width: 120px;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for sitemap in sitemaps %}
                        <tr>
                            <td>{{ sitemap.id }}</td>
                            <td>
                                <a href="{{ sitemap.url }}" target="_blank" class="text-decoration-none" 
                                   title="{{ sitemap.url }}" data-bs-toggle="tooltip">
                                    {{ sitemap.url|truncatechars:45 }}
                                    <i class="fas fa-external-link-alt ms-1 text-muted"></i>
                                </a>
                            </td>
                            <td>
                                <strong>{{ sitemap.title|truncatechars:40 }}</strong>
                            </td>
                            <td>
                                {% if sitemap.content_type_ai %}
                                    <span class="content-type-badge content-type-{{ sitemap.content_type_ai }}">
                                        {{ sitemap.get_content_type_ai_display }}
                                    </span>
                                {% else %}
                                    <span class="content-type-badge content-type-unknown">
                                        未判定
                                    </span>
                                {% endif %}
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ sitemap.created_at|date:"m-d H:i" }}
                                </small>
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ sitemap.last_crawl_time|date:"m-d H:i"|default:"未抓取" }}
                                </small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'website:sitemap_detail' sitemap.id %}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> 详情
                                    </a>
                                    <button type="button" 
                                            class="btn btn-sm btn-outline-success ai-analyze-btn"
                                            data-sitemap-id="{{ sitemap.id }}"
                                            data-url="{{ sitemap.url }}"
                                            title="AI内容类型判定">
                                        <i class="fas fa-robot"></i> AI
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">没有找到符合条件的页面</h5>
                {% if search_query or content_type %}
                    <p class="text-muted">请尝试修改搜索条件</p>
                    <a href="{% url 'website:site_detail' site.id %}" class="btn btn-outline-primary">
                        <i class="fas fa-times"></i> 清除筛选
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

<!-- 分页 -->
{% if sitemaps.has_other_pages %}
    <nav aria-label="页面分页">
        <ul class="pagination">
            {% if sitemaps.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if content_type %}&content_type={{ content_type }}{% endif %}">
                        <i class="fas fa-angle-double-left"></i>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ sitemaps.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if content_type %}&content_type={{ content_type }}{% endif %}">
                        <i class="fas fa-angle-left"></i>
                    </a>
                </li>
            {% endif %}
            
            {% for page_num in sitemaps.paginator.page_range %}
                {% if page_num == sitemaps.number %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                {% elif page_num > sitemaps.number|add:'-3' and page_num < sitemaps.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if content_type %}&content_type={{ content_type }}{% endif %}">
                            {{ page_num }}
                        </a>
                    </li>
                {% endif %}
            {% endfor %}
            
            {% if sitemaps.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ sitemaps.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if content_type %}&content_type={{ content_type }}{% endif %}">
                        <i class="fas fa-angle-right"></i>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ sitemaps.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if content_type %}&content_type={{ content_type }}{% endif %}">
                        <i class="fas fa-angle-double-right"></i>
                    </a>
                </li>
            {% endif %}
        </ul>
    </nav>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 启用Bootstrap工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // 批量AI分析按钮点击事件
    document.getElementById('batch-ai-analyze-btn').addEventListener('click', function() {
        const limit = parseInt(document.getElementById('batch-limit').value) || 20;
        
        if (limit < 1 || limit > 100) {
            showMessage('error', '判定数量必须在1-100之间');
            return;
        }
        
        startBatchAnalysis(limit);
    });
    
    // 规则测试按钮点击事件
    document.querySelectorAll('.test-rule-btn').forEach(function(button) {
        button.addEventListener('click', function() {
            const pattern = this.getAttribute('data-pattern');
            const contentType = this.getAttribute('data-content-type');
            
            // 显示测试区域
            const testArea = document.getElementById('rule-test-area');
            testArea.style.display = 'block';
            
            // 设置当前测试的规则
            document.getElementById('test-url').setAttribute('data-current-pattern', pattern);
            document.getElementById('test-url').setAttribute('data-current-content-type', contentType);
            
            // 滚动到测试区域
            testArea.scrollIntoView({ behavior: 'smooth' });
        });
    });
    
    // AI分析按钮点击事件
    document.querySelectorAll('.ai-analyze-btn').forEach(function(button) {
        button.addEventListener('click', function() {
            const sitemapId = this.getAttribute('data-sitemap-id');
            const url = this.getAttribute('data-url');
            
            // 禁用按钮并显示加载状态
            const originalHtml = this.innerHTML;
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 分析中';
            
            // 发起AJAX请求
            fetch(`/website/step/update_single_url_content_type/${sitemapId}/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新页面上的内容类型显示
                    const row = this.closest('tr');
                    const contentTypeCell = row.querySelector('td:nth-child(4)');
                    
                    // 更新内容类型标签
                    const newBadgeClass = `content-type-badge content-type-${data.new_content_type}`;
                    contentTypeCell.innerHTML = `<span class="${newBadgeClass}">${data.content_type_label}</span>`;
                    
                    // 显示成功消息
                    showMessage('success', `AI分析完成：${data.content_type_label}`);
                } else {
                    showMessage('error', `分析失败：${data.error}`);
                }
            })
            .catch(error => {
                console.error('请求失败:', error);
                showMessage('error', '请求失败，请稍后重试');
            })
            .finally(() => {
                // 恢复按钮状态
                this.disabled = false;
                this.innerHTML = originalHtml;
            });
        });
    });
});

// 获取CSRF Token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// 显示消息提示
function showMessage(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // 在页面顶部显示消息
    const container = document.querySelector('.container.main-content');
    const breadcrumb = container.querySelector('nav[aria-label="breadcrumb"]');
    if (breadcrumb) {
        breadcrumb.insertAdjacentHTML('afterend', alertHtml);
    } else {
        container.insertAdjacentHTML('afterbegin', alertHtml);
    }
    
    // 5秒后自动隐藏
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}

// 批量AI分析函数
function startBatchAnalysis(limit) {
    const batchBtn = document.getElementById('batch-ai-analyze-btn');
    const progressContainer = document.getElementById('batch-progress-container');
    const resultsContainer = document.getElementById('batch-results-container');
    const progressBar = document.getElementById('batch-progress-bar');
    const progressText = document.getElementById('batch-progress-text');
    const batchStatus = document.getElementById('batch-status');
    
    // 禁用按钮并显示加载状态
    const originalBtnHtml = batchBtn.innerHTML;
    batchBtn.disabled = true;
    batchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
    
    // 显示进度容器
    progressContainer.style.display = 'block';
    resultsContainer.style.display = 'block';
    
    // 重置进度和结果
    progressBar.style.width = '0%';
    progressText.textContent = '0/0';
    batchStatus.innerHTML = '<small>正在获取待处理URL列表...</small>';
    document.getElementById('success-count').textContent = '0';
    document.getElementById('failed-count').textContent = '0';
    document.getElementById('total-count').textContent = '0';
    
    // 调用现有的批量AI判定API
    const siteId = {{ site.id }};
    
    fetch('/website/step/update_content_type_by_ai/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({
            site_id: siteId,
            limit: limit
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 更新结果统计
            document.getElementById('success-count').textContent = data.successful_count || 0;
            document.getElementById('failed-count').textContent = data.failed_count || 0;
            document.getElementById('total-count').textContent = data.total_processed || 0;
            
            // 更新进度条到100%
            progressBar.style.width = '100%';
            progressText.textContent = `${data.total_processed}/${data.total_processed}`;
            
            if (data.interrupted) {
                batchStatus.innerHTML = '<small class="text-warning">处理被中断，已完成部分URL的分析</small>';
                showMessage('warning', `批量分析被中断，已完成 ${data.total_processed} 个URL的分析`);
            } else {
                batchStatus.innerHTML = '<small class="text-success">批量分析完成！</small>';
                showMessage('success', `批量分析完成，成功分析 ${data.successful_count} 个URL`);
            }
            
            // 刷新页面以显示更新后的结果
            setTimeout(() => {
                window.location.reload();
            }, 2000);
            
        } else {
            batchStatus.innerHTML = '<small class="text-danger">批量分析失败</small>';
            showMessage('error', `批量分析失败：${data.error}`);
        }
    })
    .catch(error => {
        console.error('批量分析请求失败:', error);
        batchStatus.innerHTML = '<small class="text-danger">请求失败</small>';
        showMessage('error', '请求失败，请稍后重试');
    })
    .finally(() => {
        // 恢复按钮状态
        batchBtn.disabled = false;
        batchBtn.innerHTML = originalBtnHtml;
    });
}

// URL规则显示切换函数
function toggleUrlRules() {
    const panel = document.getElementById('url-rules-panel');
    if (panel.style.display === 'none') {
        panel.style.display = 'block';
        // 滚动到面板位置
        panel.scrollIntoView({ behavior: 'smooth' });
    } else {
        panel.style.display = 'none';
    }
}

// 测试所有规则函数
function testAllRules() {
    const testUrl = document.getElementById('test-url').value.trim();
    const resultsDiv = document.getElementById('test-results');
    
    if (!testUrl) {
        showMessage('error', '请输入要测试的URL');
        return;
    }
    
    // 获取所有规则
    const rules = {{ site.url_matching_rules|safe }};
    
    let results = '<div class="alert alert-info"><strong>测试结果：</strong></div>';
    let matchFound = false;
    
    results += '<div class="table-responsive"><table class="table table-sm">';
    results += '<thead><tr><th>规则</th><th>内容类型</th><th>匹配结果</th></tr></thead><tbody>';
    
    rules.forEach((rule, index) => {
        try {
            const regex = new RegExp(rule.pattern);
            const isMatch = regex.test(testUrl);
            
            results += `<tr class="${isMatch ? 'table-success' : 'table-light'}">`;
            results += `<td><code>${rule.pattern}</code></td>`;
            results += `<td><span class="content-type-badge content-type-${rule.content_type}">${rule.content_type}</span></td>`;
            results += `<td>`;
            
            if (isMatch) {
                results += '<i class="fas fa-check text-success"></i> 匹配';
                matchFound = true;
            } else {
                results += '<i class="fas fa-times text-muted"></i> 不匹配';
            }
            
            results += '</td></tr>';
        } catch (e) {
            results += `<tr class="table-warning">`;
            results += `<td><code>${rule.pattern}</code></td>`;
            results += `<td><span class="content-type-badge content-type-${rule.content_type}">${rule.content_type}</span></td>`;
            results += `<td><i class="fas fa-exclamation-triangle text-warning"></i> 正则表达式错误</td>`;
            results += '</tr>';
        }
    });
    
    results += '</tbody></table></div>';
    
    if (!matchFound) {
        results += '<div class="alert alert-warning mt-2">该URL没有匹配任何规则</div>';
    } else {
        results += '<div class="alert alert-success mt-2">找到匹配的规则！</div>';
    }
    
    resultsDiv.innerHTML = results;
}
</script>
{% endblock %} 
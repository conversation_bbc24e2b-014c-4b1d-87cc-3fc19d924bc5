#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
栏目分页管理器

专门负责创建、查询和管理Column页面的分页规则
提供统一的分页规则管理接口
"""

import logging
from typing import Optional, Dict, Any, List
from django.db import transaction

from apps.website.beans import ColumnConfigBean, ContentListConfigBean, PaginationConfigBean
from apps.website.models import Site, SiteMap, ColumnRule
from apps.website.model.column import Column
from apps.website.service.column_browser_pagination_handler import ColumnBrowserPaginationHandler
from apps.website.service.column_page_extractor import ColumnPageExtractor

logger = logging.getLogger(__name__)


class ColumnRuleManager:
    """栏目分页管理器"""

    @staticmethod
    def get_site_column_rules(site: Site, order_by: str = '-success_count') -> List[ColumnRule]:
        """
        获取指定站点下的所有栏目规则

        Args:
            site: 站点对象
            order_by: 排序方式，默认按成功次数降序排列

        Returns:
            List[ColumnRule]: 栏目规则列表，如果没有则返回空列表
        """
        try:
            logger.info(f"获取站点 {site.name} 的栏目规则")

            rules = ColumnRule.objects.filter(site_id=site.id).order_by(order_by)
            rules_list = list(rules)

            logger.info(f"找到 {len(rules_list)} 个栏目规则")
            return rules_list

        except Exception as e:
            logger.error(f"获取站点栏目规则失败: {str(e)}")
            return []

    @staticmethod
    def get_column_rule(site_map: SiteMap) -> Optional[ColumnRule]:
        """
        根据SiteMap获取栏目规则（通过Column模型）

        Args:
            site_map: SiteMap对象

        Returns:
            Optional[ColumnRule]: 栏目规则，如果不存在则尝试生成新规则
        """
        try:
            # 获取或创建对应的Column
            from apps.website.service.column_service import ColumnService
            column_service = ColumnService()
            column = column_service.get_or_create_column(site_map)

            # 首先查找Column已关联的规则
            if column.column_rule_id:
                try:
                    rule = ColumnRule.objects.get(id=column.column_rule_id)
                    logger.info(f"找到Column关联的栏目规则: {rule.name}")
                    return rule
                except ColumnRule.DoesNotExist:
                    logger.warning(f"Column关联的栏目规则ID {column.column_rule_id} 无效")

            # 查找站点下的现有规则
            existing_rules = ColumnRuleManager.get_site_column_rules(site_map.site)
            if existing_rules:
                for rule in existing_rules:
                    #todo 尝试进行规则关联
                    column=ColumnRuleManager.try_associate_rule(site_map,rule)
                    if column is not None:
                        logger.info(f"使用站点现有最匹配规则: {rule.name}")
                        return rule

            # 如果没有现有规则，尝试生成新规则
            logger.info("未找到现有栏目规则，尝试生成新规则")
            return ColumnRuleManager.generate_pagination_rule(site_map)

        except Exception as e:
            logger.error(f"获取栏目规则失败: {str(e)}")
            return None

    @staticmethod
    def get_column_rule_without_create(site_map: SiteMap) -> Optional[ColumnRule]:
        """
        根据SiteMap获取栏目规则（只查询，不自动生成）
        
        Args:
            site_map: SiteMap对象
            
        Returns:
            Optional[ColumnRule]: 栏目规则，如果不存在则返回None
        """
        try:
            # 获取或创建对应的Column
            from apps.website.service.column_service import ColumnService
            column_service = ColumnService()
            column = column_service.get_or_create_column(site_map)

            # 首先查找Column已关联的规则
            if column.column_rule_id:
                try:
                    rule = ColumnRule.objects.get(id=column.column_rule_id)
                    # logger.info(f"找到Column关联的栏目规则: {rule.name}")
                    return rule
                except ColumnRule.DoesNotExist:
                    logger.warning(f"Column关联的栏目规则ID {column.column_rule_id} 无效")

            # 查找站点下的现有规则，但不自动生成
            existing_rules = ColumnRuleManager.get_site_column_rules(site_map.site)
            if existing_rules:
                for rule in existing_rules:
                    # 简单检查是否可能匹配（不执行实际关联）
                    if ColumnRuleManager._can_rule_match_sitemap(site_map, rule):
                        logger.info(f"找到可能匹配的站点规则: {rule.name}")
                        return rule

            # 没有找到任何规则，直接返回None（不自动生成）
            return None

        except Exception as e:
            logger.error(f"查询栏目规则失败: {str(e)}")
            return None

    @staticmethod
    def _can_rule_match_sitemap(site_map: SiteMap, column_rule: ColumnRule) -> bool:
        """
        简单检查规则是否可能匹配SiteMap（不执行实际关联操作）
        
        Args:
            site_map: SiteMap对象
            column_rule: 栏目规则对象
            
        Returns:
            bool: 是否可能匹配
        """
        # 这里可以添加简单的匹配逻辑，比如检查域名、URL模式等
        # 暂时返回True，表示可能匹配
        return True


    
    @staticmethod
    def try_associate_rule(site_map: SiteMap, column_rule: ColumnRule) -> Optional[Column]:
        """
        尝试使用栏目规则和sitemap进行匹配,如果匹配成功, 则在column中创建一条记录并关联

        Args:
            site_map: SiteMap对象
            column_rule: 栏目规则对象

        Returns:
            Optional[Column]: 创建的Column对象，失败时返回None
        """
        try:
            logger.info(f"尝试关联栏目规则: {column_rule.name} -> {site_map.url}")

            # 从栏目规则配置创建ColumnConfigBean
            column_config_bean = ColumnConfigBean.from_dict(column_rule.rule_config)
            # 向取出sitemap的 html进行 content 的提取测试，如果这个测试失败了，就不用进行后续的提取了
            html_content = site_map.get_html_content()
            if not html_content:
                logger.error("无法获取HTML内容")
                raise ValueError("无法获取HTML内容")
            content_items = ColumnPageExtractor.extract_content(
                html_content=html_content,
                column_config_bean=column_config_bean,
                page_url=site_map.url)
            if len(content_items) == 0:
                logger.error("无法提取内容，测试失败")
                return None

            # 测试栏目规则是否适用于当前SiteMap
            test_result = ColumnRuleManager.test_pagination_rule(site_map, column_config_bean)
            if not test_result:
                logger.error(f"栏目规则测试失败: {column_rule.name} 不适用于 {site_map.url}")
                return None

            # 测试通过，创建或获取Column并关联规则
            from apps.website.service.column_service import ColumnService
            column_service = ColumnService()
            column = column_service.get_or_create_column(site_map)

            # 关联栏目规则到Column
            column.column_rule_id = column_rule.id
            column.status = 'active'
            column.save(update_fields=['column_rule_id', 'status'])

            # 更新栏目规则的成功次数
            column_rule.success_count += 1
            column_rule.save(update_fields=['success_count'])

            logger.info(f"栏目规则关联成功: Column {column.name} 已关联规则 {column_rule.name}")
            return column

        except Exception as e:
            logger.error(f"关联栏目规则失败: {str(e)}")
            return None

    @staticmethod
    def generate_pagination_rule(site_map: SiteMap) -> Optional[ColumnRule]:
        """
        生成新的栏目规则, 使用数据库事务确保数据一致性
        如果关联规则失败, 则回滚事务; 如果成功, 则提交事务

        Args:
            site_map: SiteMap对象

        Returns:
            Optional[ColumnRule]: 生成的栏目规则，失败时返回None
        """
        try:
            logger.info(f"开始为 {site_map.url} 生成分页规则")

            # 使用数据库事务确保数据一致性
            with transaction.atomic():
                # 第1步：生成分页配置
                try:
                    column_config_bean = ColumnRuleManager._generate_pagination_config(site_map)
                except Exception as e:
                    logger.error(f"生成分页配置失败: {str(e)}")
                    return None

                # 第2步：创建分页规则（但不立即保存到数据库）
                rule = ColumnRule(
                    site_id=site_map.site_id,
                    name=f"{site_map.site.name}-{site_map.title}-分页规则",
                    source_sitemap_id=site_map.id,
                    rule_config=column_config_bean.to_dict(),  # 使用to_dict()方法
                    success_count=1  # 初始为1
                )

                # 第3步：测试分页规则
                test_result = ColumnRuleManager.test_pagination_rule(site_map, column_config_bean)
                if not test_result:
                    logger.error("分页规则测试失败，回滚事务")
                    # 抛出异常触发事务回滚
                    raise Exception("分页规则测试失败")

                # 第4步：测试通过，保存分页规则
                rule.save()
                logger.info(f"分页规则保存成功: {rule.name}")

                # 第5步：创建Column并关联规则
                from apps.website.service.column_service import ColumnService
                column_service = ColumnService()
                column = column_service.get_or_create_column(site_map)
                column.column_rule_id = rule.id
                column.status = 'active'
                column.save(update_fields=['column_rule_id', 'status'])

                logger.info(f"分页规则生成并关联成功: {rule.name} -> Column {column.name}")
                return rule

        except Exception as e:
            logger.error(f"生成分页规则失败: {str(e)}")
            # 事务会自动回滚
            return None

    @staticmethod
    def _generate_pagination_config(site_map: SiteMap) -> ColumnConfigBean:
        """
        调用AI服务生成分页配置

        Args:
            site_map: SiteMap对象

        Returns:
            ColumnConfigBean: 生成的分页配置，失败时抛出异常
        """
        try:
            # 获取HTML内容
            html_content = site_map.get_html_content()
            if not html_content:
                logger.error("无法获取HTML内容")
                raise ValueError("无法获取HTML内容")

            logger.info(f"开始为 {site_map.url} 生成AI分页配置")

            # 直接调用AI服务
            from apps.website.service.ai_service import ai_service
            
            # 调用AI服务分析翻页模式
            result = ai_service.analyze_pagination_pattern(site_map.url, html_content)
            
            if result and result.get('success'):
                column_config = result.get('column_config')
                if column_config:
                    logger.info("AI分析成功")
                    
                    # 转换为ColumnConfigBean
                    column_config_bean = ColumnConfigBean.from_dict(column_config)

                    # 验证配置
                    validation = ColumnRuleManager.validate_config(column_config_bean)
                    if not validation['valid']:
                        error_msg = f"配置验证失败: {', '.join(validation['errors'])}"
                        logger.error(error_msg)
                        raise ValueError(error_msg)
                    
                    pagination_type = column_config_bean.pagination_config.pagination_type if column_config_bean.pagination_config else "unknown"
                    logger.info(f"AI分页配置生成成功: {pagination_type}")
                    return column_config_bean
                else:
                    error_msg = "AI分析结果中没有pagination_info"
                    logger.error(error_msg)
                    raise ValueError(error_msg)
            else:
                error_msg = result.get('error', '未知错误') if result else 'AI服务无响应'
                logger.error(f"AI分析失败: {error_msg}")
                raise ValueError(f"AI分析失败: {error_msg}")
                
        except Exception as e:
            logger.error(f"生成分页配置失败: {str(e)}")
            import traceback
            traceback.print_exc()
            # 失败时直接抛出异常，不再生成默认配置
            raise

    @staticmethod
    def validate_config(column_config: ColumnConfigBean) -> Dict[str, Any]:
        """
        验证生成的配置
        
        Args:
            config: 生成的配置
            
        Returns:
            Dict: 验证结果
        """
        # 简单的验证结果，后续可以根据需要实现具体验证逻辑
        return {
            'valid': True,
            'errors': [],
            'warnings': []
        }

    @staticmethod
    def test_pagination_rule(site_map: SiteMap, column_config_bean: ColumnConfigBean) -> bool:
        """
        测试分页规则

        Args:
            site_map: SiteMap对象
            column_config_bean: 栏目配置Bean

        Returns:
            bool: 测试是否通过
        """
        handler = None
        try:
            logger.info(f"开始测试分页规则: {site_map.url}")

            # 创建处理器
            handler = ColumnBrowserPaginationHandler.createHandler(
                url=site_map.url,
                column_config=column_config_bean
            )

            # 启动浏览器并获取第一页信息
            page_pagination_info = handler.launch()
            html_content = handler.get_page_html()

            # 提取第一页内容
            first_page_content_items = ColumnPageExtractor.extract_content(
                html_content=html_content,
                column_config_bean=column_config_bean,
                page_url=handler.get_current_url()
            )

            logger.info(f"第一页提取到 {len(first_page_content_items)} 个内容项")

            # 如果第一页没有内容，测试失败
            if not first_page_content_items:
                logger.error("第一页未提取到任何内容")
                return False

            # 如果只有一页，且有内容，测试通过
            next_page_selector = column_config_bean.pagination_config.next_page if column_config_bean.pagination_config else None
            if page_pagination_info.is_end(handler.page, next_page_selector):
                logger.info("只有一页，但有内容，测试通过")
                return True

            # 测试翻页功能
            max_test_count = 3
            content_changed = False
            first_page_urls = {item.url for item in first_page_content_items}

            while not page_pagination_info.is_end(handler.page, next_page_selector) and max_test_count > 0:
                # 执行翻页
                page_pagination_info = handler.next_page()
                html_content = handler.get_page_html()

                # 提取当前页内容
                current_page_content_items = ColumnPageExtractor.extract_content(
                    html_content=html_content,
                    column_config_bean=column_config_bean,
                    page_url=handler.get_current_url()
                )

                logger.info(f"当前页提取到 {len(current_page_content_items)} 个内容项")

                # 检查内容是否发生变化
                current_page_urls = {item.url for item in current_page_content_items}
                if current_page_urls and not current_page_urls.issubset(first_page_urls):
                    content_changed = True
                    logger.info("检测到内容变化，翻页功能正常")
                    break

                max_test_count -= 1

            # 判断测试结果
            if content_changed:
                logger.info("分页规则测试通过")
                return True
            else:
                logger.warning("翻页后内容未发生变化，可能翻页功能异常")
                return False

        except Exception as e:
            logger.error(f"测试分页规则异常: {str(e)}")
            return False
        finally:
            # 清理资源
            if handler:
                try:
                    handler.close()
                except Exception as e:
                    logger.error(f"关闭处理器失败: {str(e)}")



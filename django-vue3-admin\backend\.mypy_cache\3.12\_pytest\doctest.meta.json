{"data_mtime": 1754047858, "dep_lines": [26, 32, 25, 29, 30, 31, 33, 35, 39, 40, 41, 42, 4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 23, 25, 46, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 10, 10, 5, 10, 10, 10, 10, 5, 10, 20, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest.config.argparsing", "_pytest.outcomes", "_pytest._io", "_pytest.compat", "_pytest.config", "_pytest.fixtures", "_pytest.nodes", "_pytest.pathlib", "_pytest.python", "_pytest.python_api", "_pytest.warning_types", "__future__", "bdb", "contextlib", "functools", "inspect", "os", "pathlib", "platform", "sys", "traceback", "types", "typing", "warnings", "_pytest", "doctest", "builtins", "_frozen_importlib", "_pytest._code", "_pytest._io.terminalwriter", "_pytest.main", "abc"], "hash": "6758af3fb0e5b97abe8d5b7b8a94f93a9fdc8b4f", "id": "_pytest.doctest", "ignore_all": true, "interface_hash": "0e26b08fbf41d3317b8575917ef344f6509f13bf", "mtime": 1721810337, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\_pytest\\doctest.py", "plugin_data": null, "size": 26255, "suppressed": [], "version_id": "1.17.1"}
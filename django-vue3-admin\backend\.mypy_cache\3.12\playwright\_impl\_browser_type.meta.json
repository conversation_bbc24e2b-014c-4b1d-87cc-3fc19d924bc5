{"data_mtime": 1754047860, "dep_lines": [20, 26, 27, 28, 34, 35, 45, 46, 47, 50, 15, 16, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["playwright._impl._api_structures", "playwright._impl._browser", "playwright._impl._browser_context", "playwright._impl._connection", "playwright._impl._errors", "playwright._impl._helper", "playwright._impl._json_pipe", "playwright._impl._network", "playwright._impl._waiter", "playwright._impl._playwright", "asyncio", "pathlib", "typing", "builtins", "_asyncio", "_contextvars", "_frozen_importlib", "_typeshed", "abc", "asyncio.events", "asyncio.tasks", "os", "playwright._impl._local_utils", "playwright._impl._selectors", "playwright._impl._transport", "pyee", "pyee.asyncio", "pyee.base", "re"], "hash": "c0c66a1cb3dc81f7286e76444531d21dff3cf724", "id": "playwright._impl._browser_type", "ignore_all": true, "interface_hash": "0f46a9ac6474190a3c5e79b8bd023daa7bc390ee", "mtime": 1722410462, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\playwright\\_impl\\_browser_type.py", "plugin_data": null, "size": 10106, "suppressed": [], "version_id": "1.17.1"}
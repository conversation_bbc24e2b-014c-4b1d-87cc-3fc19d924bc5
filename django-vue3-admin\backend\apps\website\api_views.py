from django.shortcuts import get_object_or_404

from rest_framework.views import APIView
from rest_framework.response import Response

from .models import Site, SiteMap, ColumnRule, Content
from .model.column import Column
from .enums import CrawlMode
from apps.website.service.column_rule_manager import ColumnRuleManager
from apps.website.service.column_browser_pagination_handler import ColumnBrowserPaginationHandler
from apps.website.service.column_service import ColumnService
from apps.website.service.site_service import SiteService
from apps.website.service.site_map_service import SiteMapService


class CrawlSitemapContentView(APIView):
    """触发抓取指定SiteMap内容"""
    def post(self, request, sitemap_id):
        sitemap = get_object_or_404(SiteMap, pk=sitemap_id)

        # 获取或生成栏目规则
        column_rule = ColumnRuleManager.get_column_rule(sitemap)
        if not column_rule:
            return Response({'status': 'error', 'message': '无法找到或生成栏目规则'}, status=404)

        # 使用栏目规则的配置
        column_config_bean = column_rule.get_column_config_bean()

        try:
            # 使用ColumnBrowserPaginationHandler进行翻页抓取
            handler = ColumnBrowserPaginationHandler.createHandler(
                url=sitemap.url,
                column_config=column_config_bean
            )
            handler.launch()
            # 这里可以添加更多抓取逻辑，例如处理多页
            html_content = handler.get_page_html()
            handler.close()

            return Response({'status': 'success', 'message': '抓取任务已启动', 'html_content': html_content[:500]})

        except Exception as e:
            return Response({'status': 'error', 'message': str(e)}, status=500)


class CrawlColumnContentView(APIView):
    """抓取栏目内容接口"""
    
    def post(self, request, sitemap_id):
        """
        深度抓取栏目内容
        
        参数:
        - sitemap_id: SiteMap ID
        - crawl_mode: 抓取模式 ('deep' 或 'update')
        - column_id: 栏目ID（可选）
        """
        try:
            sitemap = get_object_or_404(SiteMap, pk=sitemap_id)
            crawl_mode = request.data.get('crawl_mode', 'deep')
            column_id = request.data.get('column_id')
            
            # 获取栏目规则
            column_rule = ColumnRuleManager.get_column_rule(sitemap)
            if not column_rule:
                return Response({
                    'success': False,
                    'error': '无法找到栏目规则，请先生成规则'
                }, status=400)
            
            # 获取或创建Column对象
            column_service = ColumnService()
            column = column_service.get_or_create_column(sitemap)
            
            # 设置抓取模式
            crawl_mode_enum = CrawlMode.DEEP_CRAWL if crawl_mode == 'deep' else CrawlMode.UPDATE_CRAWL
            
            # 调用栏目抓取服务
            result = column_service.crawl_colum(
                column=column,
                max_pages=column.max_crawl_pages if column else 10,
                mode=crawl_mode_enum
            )
            
            if result['success']:
                return Response({
                    'success': True,
                    'message': f'栏目内容抓取完成',
                    'crawled_count': result.get('content_items_saved', 0),
                    'total_pages': result.get('pages_crawled', 0),
                    'content_items_found': result.get('content_items_found', 0),
                    'processing_time': result.get('processing_time', 0)
                })
            else:
                return Response({
                    'success': False,
                    'error': result.get('error_message', '抓取失败')
                }, status=500)
                
        except Exception as e:
            return Response({
                'success': False,
                'error': f'抓取过程中发生异常: {str(e)}'
            }, status=500)


class UpdateColumnContentView(APIView):
    """更新栏目内容接口"""
    
    def post(self, request, sitemap_id):
        """
        增量更新栏目内容
        
        参数:
        - sitemap_id: SiteMap ID
        - column_id: 栏目ID（可选）
        """
        try:
            sitemap = get_object_or_404(SiteMap, pk=sitemap_id)
            column_id = request.data.get('column_id')
            
            # 获取栏目规则
            column_rule = ColumnRuleManager.get_column_rule(sitemap)
            if not column_rule:
                return Response({
                    'success': False,
                    'error': '无法找到栏目规则，请先生成规则'
                }, status=400)
            
            # 获取或创建Column对象
            column_service = ColumnService()
            column = column_service.get_or_create_column(sitemap)
            
            # 调用栏目更新服务
            result = column_service.crawl_colum(
                column=column,
                max_pages=5,  # 更新模式通常只需要抓取前几页
                mode=CrawlMode.UPDATE_CRAWL
            )
            
            if result['success']:
                return Response({
                    'success': True,
                    'message': f'栏目内容更新完成',
                    'crawled_count': result.get('content_items_saved', 0),
                    'total_pages': result.get('pages_crawled', 0),
                    'content_items_found': result.get('content_items_found', 0),
                    'processing_time': result.get('processing_time', 0)
                })
            else:
                return Response({
                    'success': False,
                    'error': result.get('error_message', '更新失败')
                }, status=500)
                
        except Exception as e:
            return Response({
                'success': False,
                'error': f'更新过程中发生异常: {str(e)}'
            }, status=500)


class CreateSiteView(APIView):
    """站点初始化接口"""
    
    def post(self, request):
        """
        创建站点并进行深度初始化
        
        参数:
        - name: 站点名称
        - domain: 站点域名
        - start_url: 起始URL
        - description: 站点描述（可选）
        """
        try:
            # 获取参数
            name = request.data.get('name')
            domain = request.data.get('domain')
            start_url = request.data.get('start_url')
            description = request.data.get('description', '')
            
            # 参数验证
            if not all([name, domain, start_url]):
                return Response({
                    'success': False,
                    'error': '缺少必需参数: name, domain, start_url'
                }, status=400)
            
            # 构建站点配置
            site_config = {
                'name': name,
                'domain': domain,
                'start_url': start_url,
                'description': description
            }
            
            # 调用站点初始化服务
            site_service = SiteService()
            result = site_service.step01_create_site(site_config)
            
            if result['success']:
                return Response({
                    'success': True,
                    'message': '站点初始化成功',
                    'site_id': result['site'].id,
                    'site_name': result['site'].name,
                    'statistics': result.get('statistics', {}),
                    'created': result.get('created', False)
                })
            else:
                return Response({
                    'success': False,
                    'error': result.get('error', '站点初始化失败')
                }, status=500)
                
        except Exception as e:
            return Response({
                'success': False,
                'error': f'站点初始化时发生异常: {str(e)}'
            }, status=500)


class GenerateRulesToSitemapView(APIView):
    """规则生成与应用接口"""
    
    def post(self, request):
        """
        AI生成内容URL正则规则并应用分类
        
        参数:
        - site_id: 站点ID
        """
        try:
            site_id = request.data.get('site_id')
            
            if not site_id:
                return Response({
                    'success': False,
                    'error': '必须提供site_id参数'
                }, status=400)
            
            site = get_object_or_404(Site, pk=site_id)
            
            # 调用规则生成与应用服务
            site_map_service = SiteMapService() 
            result = site_map_service.step02_2_apply_rules_to_sitemap(site)
            
            if result['success']:
                return Response({
                    'success': True,
                    'message': '规则生成与应用成功',
                    'site_id': site_id,
                    'site_name': site.name,
                    'total_sitemaps': result.get('total_sitemaps', 0),
                    'before_stats': result.get('before_stats', {}),
                    'after_stats': result.get('after_stats', {}),
                    'processing_time': result.get('processing_time', 0)
                })
            else:
                return Response({
                    'success': False,
                    'error': result.get('error', '规则生成与应用失败')
                }, status=500)
                
        except Exception as e:
            return Response({
                'success': False,
                'error': f'规则生成与应用时发生异常: {str(e)}'
            }, status=500)


class UpdateContentTypeByAIView(APIView):
    """AI内容类型判断接口"""
    
    def post(self, request):
        """
        对非内容URL进行AI智能分类
        
        参数:
        - site_id: 站点ID
        - limit: 处理数量限制，默认50
        """
        try:
            site_id = request.data.get('site_id')
            limit = request.data.get('limit', 50)
            
            if not site_id:
                return Response({
                    'success': False,
                    'error': '必须提供site_id参数'
                }, status=400)
            
            # 验证limit参数
            try:
                limit = int(limit)
                if limit <= 0:
                    limit = 50
            except (ValueError, TypeError):
                limit = 50
            
            site = get_object_or_404(Site, pk=site_id)
            
            # 调用AI内容类型判断服务
            site_map_service = SiteMapService()
            result = site_map_service.step03_update_content_type_by_ai(site, limit)
            
            if result['success']:
                return Response({
                    'success': True,
                    'message': 'AI内容类型判断完成',
                    'site_id': site_id,
                    'site_name': site.name,
                    'total_processed': result.get('total_processed', 0),
                    'successful_count': result.get('successful_count', 0),
                    'failed_count': result.get('failed_count', 0),
                    'processing_time': result.get('processing_time', 0),
                    'content_type_stats': result.get('content_type_stats', {}),
                    'interrupted': result.get('interrupted', False)
                })
            else:
                return Response({
                    'success': False,
                    'error': result.get('error', 'AI内容类型判断失败')
                }, status=500)
                
        except Exception as e:
            return Response({
                'success': False,
                'error': f'AI内容类型判断时发生异常: {str(e)}'
            }, status=500)


class ColumnRuleDetailView(APIView):
    """栏目规则详情接口"""
    
    def get(self, request, rule_id):
        """
        获取栏目规则详情
        
        参数:
        - rule_id: 栏目规则ID
        """
        try:
            rule = get_object_or_404(ColumnRule, pk=rule_id)
            
            # 获取使用此规则的栏目统计
            from apps.website.model.column import Column
            columns_using_rule = Column.objects.filter(column_rule_id=rule_id).count()
            
            # 获取规则配置详情
            rule_config = rule.rule_config or {}
            
            # 提取分页配置信息
            pagination_config = rule_config.get('pagination_config', {})
            content_list_config = rule_config.get('content_list_config', {})
            
            return Response({
                'success': True,
                'rule': {
                    'id': rule.id,
                    'name': rule.name,
                    'site_id': rule.site_id,
                    'site_name': rule.site.name if hasattr(rule, 'site') else 'Unknown',
                    'source_sitemap_id': rule.source_sitemap_id,
                    'success_count': rule.success_count,
                    'failed_count': rule.failed_count,
                    'test_count': rule.test_count,
                    'created_at': rule.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'updated_at': rule.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'columns_using_count': columns_using_rule,
                    
                    # 分页配置
                    'pagination_type': pagination_config.get('pagination_type', '未设置'),
                    'next_page_selector': pagination_config.get('next_page', '未设置'),
                    'max_pages': pagination_config.get('max_pages', '未设置'),
                    
                    # 内容提取配置
                    'content_container_selector': content_list_config.get('container_selector', '未设置'),
                    'content_item_selector': content_list_config.get('item_selector', '未设置'),
                    'title_selector': content_list_config.get('title_selector', '未设置'),
                    'url_selector': content_list_config.get('url_selector', '未设置'),
                    'date_selector': content_list_config.get('date_selector', '未设置'),
                    
                    # 完整配置JSON
                    'full_config': rule_config
                }
            })
            
        except Exception as e:
            return Response({
                'success': False,
                'error': f'获取规则详情时发生异常: {str(e)}'
            }, status=500)


class GenerateColumnRuleView(APIView):
    """为指定的SiteMap生成栏目规则"""
    
    def post(self, request, sitemap_id):
        """
        为指定的SiteMap生成栏目规则
        
        参数:
        - sitemap_id: SiteMap的ID，必须是content_type_ai为'content_section'的栏目页
        """
        try:
            sitemap = get_object_or_404(SiteMap, pk=sitemap_id)
            
            # 验证SiteMap是否为栏目页类型
            if sitemap.content_type_ai != 'content_section':
                return Response({
                    'success': False,
                    'error': f'SiteMap类型必须为content_section，当前类型为: {sitemap.content_type_ai}'
                }, status=400)
            
            # 检查是否已有栏目规则
            existing_rule = ColumnRuleManager.get_column_rule(sitemap)
            if existing_rule:
                return Response({
                    'success': True,
                    'message': '该SiteMap已有对应的栏目规则',
                    'rule_id': existing_rule.id,
                    'rule_name': existing_rule.name,
                    'already_exists': True
                })
            
            # 生成新的栏目规则
            column_rule = ColumnRuleManager.generate_pagination_rule(sitemap)
            
            if column_rule:
                return Response({
                    'success': True,
                    'message': '栏目规则生成成功',
                    'rule_id': column_rule.id,
                    'rule_name': column_rule.name,
                    'rule_config': column_rule.rule_config,
                    'already_exists': False
                })
            else:
                return Response({
                    'success': False,
                    'error': '栏目规则生成失败，请查看日志获取详细信息'
                }, status=500)
                
        except Exception as e:
            return Response({
                'success': False,
                'error': f'生成栏目规则时发生异常: {str(e)}'
            }, status=500)


class AssociateColumnRulesView(APIView):
    """将现有栏目规则与站点下的栏目进行关联匹配"""
    
    def post(self, request):
        """
        将栏目规则与现有栏目进行关联
        
        参数:
        - site_id: 站点ID，可选
        - rule_id: 指定栏目规则ID，可选
        """
        try:
            site_id = request.data.get('site_id')
            rule_id = request.data.get('rule_id')
            
            if not site_id:
                return Response({
                    'success': False,
                    'error': '必须提供site_id参数'
                }, status=400)
            
            site = get_object_or_404(Site, pk=site_id)
            
            # 获取所有未关联规则的栏目页SiteMap
            content_section_sitemaps = SiteMap.objects.filter(
                site_id=site_id,
                content_type_ai='content_section'
            )
            
            # 获取要尝试关联的规则
            if rule_id:
                # 指定规则ID
                rules_to_try = [get_object_or_404(ColumnRule, pk=rule_id)]
            else:
                # 获取站点下所有规则，按成功次数排序
                rules_to_try = ColumnRule.objects.filter(site_id=site_id).order_by('-success_count')
            
            if not rules_to_try:
                return Response({
                    'success': False,
                    'error': f'站点 {site.name} 下没有可用的栏目规则'
                }, status=400)
            
            results = {
                'success': True,
                'site_name': site.name,
                'total_sitemaps': content_section_sitemaps.count(),
                'associations': [],
                'failed_associations': []
            }
            
            # 尝试关联每个栏目页
            for sitemap in content_section_sitemaps:
                association_success = False
                
                for rule in rules_to_try:
                    column = ColumnRuleManager.try_associate_rule(sitemap, rule)
                    if column:
                        results['associations'].append({
                            'sitemap_id': sitemap.id,
                            'sitemap_url': sitemap.url,
                            'sitemap_title': sitemap.title,
                            'rule_id': rule.id,
                            'rule_name': rule.name,
                            'column_id': column.id,
                            'column_name': column.name
                        })
                        association_success = True
                        break
                
                if not association_success:
                    results['failed_associations'].append({
                        'sitemap_id': sitemap.id,
                        'sitemap_url': sitemap.url,
                        'sitemap_title': sitemap.title,
                        'reason': '没有适用的栏目规则'
                    })
            
            results['successful_count'] = len(results['associations'])
            results['failed_count'] = len(results['failed_associations'])
            
            return Response(results)
            
        except Exception as e:
            return Response({
                'success': False,
                'error': f'关联栏目规则时发生异常: {str(e)}'
            }, status=500)


class CrawlColumnInitView(APIView):
    """栏目初始化抓取接口"""
    
    def post(self, request):
        """
        对栏目进行初始化抓取
        
        参数:
        - column_id: 栏目ID
        - max_pages: 最大抓取页数，默认10页
        """
        try:
            column_id = request.data.get('column_id')
            max_pages = request.data.get('max_pages', 10)
            
            if not column_id:
                return Response({
                    'success': False,
                    'error': '必须提供column_id参数'
                }, status=400)
            
            column = get_object_or_404(Column, pk=column_id)
            
            # 验证栏目是否有对应的规则
            if not column.column_rule_id:
                return Response({
                    'success': False,
                    'error': f'栏目 {column.name} 没有关联的栏目规则，请先生成或关联栏目规则'
                }, status=400)
            
            # 使用深度抓取模式进行初始化抓取
            column_service = ColumnService()
            result = column_service.crawl_colum(
                column=column,
                max_pages=max_pages,
                mode=CrawlMode.DEEP_CRAWL
            )
            
            return Response({
                'success': result['success'],
                'column_id': column_id,
                'column_name': column.name,
                'crawl_result': result
            })
            
        except Exception as e:
            return Response({
                'success': False,
                'error': f'栏目初始化抓取时发生异常: {str(e)}'
            }, status=500)


class CrawlColumnUpdateView(APIView):
    """栏目更新抓取接口"""
    
    def post(self, request):
        """
        对栏目进行增量更新抓取
        
        参数:
        - column_id: 栏目ID
        - max_pages: 最大抓取页数，默认5页（增量抓取通常不需要很多页）
        """
        try:
            column_id = request.data.get('column_id')
            max_pages = request.data.get('max_pages', 5)
            
            if not column_id:
                return Response({
                    'success': False,
                    'error': '必须提供column_id参数'
                }, status=400)
            
            column = get_object_or_404(Column, pk=column_id)
            
            # 验证栏目是否有对应的规则
            if not column.column_rule_id:
                return Response({
                    'success': False,
                    'error': f'栏目 {column.name} 没有关联的栏目规则，请先生成或关联栏目规则'
                }, status=400)
            
            # 使用增量抓取模式进行更新抓取
            column_service = ColumnService()
            result = column_service.crawl_colum(
                column=column,
                max_pages=max_pages,
                mode=CrawlMode.UPDATE_CRAWL
            )
            
            return Response({
                'success': result['success'],
                'column_id': column_id,
                'column_name': column.name,
                'crawl_result': result
            })
            
        except Exception as e:
            return Response({
                'success': False,
                'error': f'栏目更新抓取时发生异常: {str(e)}'
            }, status=500)


class UpdateSingleUrlContentTypeView(APIView):
    """单个URL的AI内容类型判断接口"""
    
    def post(self, request, sitemap_id):
        """
        对单个SiteMap URL进行AI内容类型判断
        
        参数:
        - sitemap_id: SiteMap的ID
        """
        try:
            sitemap = get_object_or_404(SiteMap, pk=sitemap_id)
            
            # 调用AI内容类型判断服务
            site_map_service = SiteMapService()
            
            # 创建临时的Site对象来调用服务
            result = site_map_service.step03_update_content_type_by_ai_single(sitemap)
            
            if result['success']:
                # 刷新sitemap对象获取最新数据
                sitemap.refresh_from_db()
                
                return Response({
                    'success': True,
                    'message': 'AI内容类型判断完成',
                    'sitemap_id': sitemap_id,
                    'old_content_type': result.get('old_content_type', ''),
                    'new_content_type': sitemap.content_type_ai,
                    'content_type_label': sitemap.get_content_type_ai_display(),
                    'processing_time': result.get('processing_time', 0)
                })
            else:
                return Response({
                    'success': False,
                    'error': result.get('error', 'AI内容类型判断失败')
                }, status=500)
                
        except Exception as e:
            return Response({
                'success': False,
                'error': f'AI内容类型判断时发生异常: {str(e)}'
            }, status=500)


class ColumnRuleDetailView(APIView):
    """栏目规则详情接口"""
    
    def get(self, request, rule_id):
        """
        获取栏目规则详情
        
        参数:
        - rule_id: 栏目规则ID
        """
        try:
            rule = get_object_or_404(ColumnRule, pk=rule_id)
            
            # 获取使用此规则的栏目统计
            from apps.website.model.column import Column
            columns_using_rule = Column.objects.filter(column_rule_id=rule_id).count()
            
            # 获取规则配置详情
            rule_config = rule.rule_config or {}
            
            # 提取分页配置信息
            pagination_config = rule_config.get('pagination_config', {})
            content_list_config = rule_config.get('content_list_config', {})
            
            return Response({
                'success': True,
                'rule': {
                    'id': rule.id,
                    'name': rule.name,
                    'site_id': rule.site_id,
                    'site_name': rule.site.name if hasattr(rule, 'site') else 'Unknown',
                    'source_sitemap_id': rule.source_sitemap_id,
                    'success_count': rule.success_count,
                    'failed_count': rule.failed_count,
                    'test_count': rule.test_count,
                    'created_at': rule.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'updated_at': rule.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'columns_using_count': columns_using_rule,
                    
                    # 分页配置
                    'pagination_type': pagination_config.get('pagination_type', '未设置'),
                    'next_page_selector': pagination_config.get('next_page', '未设置'),
                    'max_pages': pagination_config.get('max_pages', '未设置'),
                    
                    # 内容提取配置
                    'content_container_selector': content_list_config.get('container_selector', '未设置'),
                    'content_item_selector': content_list_config.get('item_selector', '未设置'),
                    'title_selector': content_list_config.get('title_selector', '未设置'),
                    'url_selector': content_list_config.get('url_selector', '未设置'),
                    'date_selector': content_list_config.get('date_selector', '未设置'),
                    
                    # 完整配置JSON
                    'full_config': rule_config
                }
            })
            
        except Exception as e:
            return Response({
                'success': False,
                'error': f'获取规则详情时发生异常: {str(e)}'
            }, status=500)
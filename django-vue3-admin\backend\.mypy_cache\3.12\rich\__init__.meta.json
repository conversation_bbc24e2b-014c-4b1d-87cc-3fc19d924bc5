{"data_mtime": 1754047860, "dep_lines": [6, 11, 3, 4, 1, 1, 1, 1], "dep_prios": [5, 25, 10, 5, 5, 30, 30, 30], "dependencies": ["rich._extension", "rich.console", "os", "typing", "builtins", "_frozen_importlib", "abc", "posixpath"], "hash": "6a2f8939b00711014e55be36953b367bb8f90c18", "id": "rich", "ignore_all": true, "interface_hash": "e96670a22c61bb16b0e1c901e08a029059f9df0a", "mtime": 1747034635, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\rich\\__init__.py", "plugin_data": null, "size": 6066, "suppressed": [], "version_id": "1.17.1"}
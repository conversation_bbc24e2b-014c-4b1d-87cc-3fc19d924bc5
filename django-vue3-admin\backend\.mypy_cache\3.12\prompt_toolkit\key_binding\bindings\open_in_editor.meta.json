{"data_mtime": 1754047859, "dep_lines": [10, 9, 7, 5, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["prompt_toolkit.key_binding.bindings.named_commands", "prompt_toolkit.key_binding.key_bindings", "prompt_toolkit.filters", "__future__", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "6417d6e76e5d6b3b9b0f5590b9e6380d5304cf02", "id": "prompt_toolkit.key_binding.bindings.open_in_editor", "ignore_all": true, "interface_hash": "fb9348658e287b3489da563be361840a1c816944", "mtime": 1722816426, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\prompt_toolkit\\key_binding\\bindings\\open_in_editor.py", "plugin_data": null, "size": 1356, "suppressed": [], "version_id": "1.17.1"}
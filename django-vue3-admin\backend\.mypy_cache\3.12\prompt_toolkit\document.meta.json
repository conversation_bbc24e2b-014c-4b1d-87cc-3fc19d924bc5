{"data_mtime": 1754047859, "dep_lines": [13, 14, 15, 5, 7, 8, 9, 10, 11, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 30, 30, 30, 30], "dependencies": ["prompt_toolkit.clipboard", "prompt_toolkit.filters", "prompt_toolkit.selection", "__future__", "bisect", "re", "string", "weakref", "typing", "builtins", "_frozen_importlib", "abc", "enum", "prompt_toolkit.clipboard.base"], "hash": "4fcaa5dc52727d2313498531d503f816ec046d11", "id": "prompt_toolkit.document", "ignore_all": true, "interface_hash": "d6a473be404831757f412968bc6ee014fcfc6be9", "mtime": 1722816426, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\prompt_toolkit\\document.py", "plugin_data": null, "size": 40579, "suppressed": [], "version_id": "1.17.1"}
{"data_mtime": 1754047847, "dep_lines": [3, 5, 1, 1, 1], "dep_prios": [5, 5, 5, 30, 30], "dependencies": ["__future__", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "263f15f7f35788b7178394b44ec91ac4ef1c935e", "id": "traitlets.utils.nested_update", "ignore_all": true, "interface_hash": "bb9ee6a660f7ba44e7abf55035e4beb256f60ce2", "mtime": 1722816422, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\traitlets\\utils\\nested_update.py", "plugin_data": null, "size": 1114, "suppressed": [], "version_id": "1.17.1"}
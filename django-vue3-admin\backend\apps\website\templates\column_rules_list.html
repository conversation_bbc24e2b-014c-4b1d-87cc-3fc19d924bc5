{% extends 'base.html' %}

{% block title %}{{ site.name }} - 栏目规则管理{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'website:site_list' %}">站点列表</a></li>
        <li class="breadcrumb-item"><a href="{% url 'website:site_detail' site.id %}">{{ site.name }}</a></li>
        <li class="breadcrumb-item active">栏目规则管理</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<!-- 站点信息 -->
<div class="card mb-4">
    <div class="card-header">
        <i class="fas fa-server"></i> 站点信息
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <h5>{{ site.name }}</h5>
                <p class="text-muted">{{ site.description|default:"无描述" }}</p>
                <p><strong>域名：</strong> {{ site.domain }}</p>
            </div>
            <div class="col-md-4">
                <div class="stats-card">
                    <h3>{{ total_rules }}</h3>
                    <p><i class="fas fa-cogs"></i> 栏目规则总数</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 栏目规则列表 -->
<div class="card">
    <div class="card-header">
        <i class="fas fa-cogs"></i> 栏目规则列表
        <span class="badge bg-info ms-2">{{ column_rules.paginator.count }} 个规则</span>
        <div class="float-end">
            <a href="{% url 'website:site_detail' site.id %}" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> 返回站点详情
            </a>
        </div>
    </div>
    <div class="card-body p-0">
        {% if column_rules %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th style="width: 60px;">ID</th>
                            <th>规则名称</th>
                            <th style="width: 120px;">分页类型</th>
                            <th style="width: 100px;">成功次数</th>
                            <th style="width: 100px;">失败次数</th>
                            <th style="width: 100px;">测试次数</th>
                            <th style="width: 120px;">创建时间</th>
                            <th style="width: 100px;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for rule in column_rules %}
                        <tr>
                            <td>{{ rule.id }}</td>
                            <td>
                                <strong>{{ rule.name }}</strong>
                                {% if rule.rule_config %}
                                    <br><small class="text-muted">
                                        配置项: {{ rule.rule_config.keys|length }} 个
                                    </small>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-primary">
                                    {{ rule.pagination_type|default:"未设置" }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-success">{{ rule.success_count }}</span>
                            </td>
                            <td>
                                <span class="badge bg-danger">{{ rule.failed_count }}</span>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ rule.test_count }}</span>
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ rule.created_at|date:"m-d H:i" }}
                                </small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-info view-rule-btn"
                                            data-rule-id="{{ rule.id }}"
                                            data-rule-name="{{ rule.name }}"
                                            data-bs-toggle="modal" 
                                            data-bs-target="#ruleDetailModal"
                                            title="查看规则详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-warning test-rule-btn"
                                            data-rule-id="{{ rule.id }}"
                                            title="测试规则">
                                        <i class="fas fa-vial"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-cogs fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无栏目规则</h5>
                <p class="text-muted">请先在栏目列表中为内容栏目页生成规则</p>
                <a href="{% url 'website:site_columns_list' site.id %}" class="btn btn-primary">
                    <i class="fas fa-list"></i> 去查看栏目列表
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- 分页 -->
{% if column_rules.has_other_pages %}
    <nav aria-label="栏目规则分页" class="mt-3">
        <ul class="pagination">
            {% if column_rules.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1">
                        <i class="fas fa-angle-double-left"></i>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ column_rules.previous_page_number }}">
                        <i class="fas fa-angle-left"></i>
                    </a>
                </li>
            {% endif %}
            
            {% for page_num in column_rules.paginator.page_range %}
                {% if page_num == column_rules.number %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                {% elif page_num > column_rules.number|add:'-3' and page_num < column_rules.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_num }}">{{ page_num }}</a>
                    </li>
                {% endif %}
            {% endfor %}
            
            {% if column_rules.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ column_rules.next_page_number }}">
                        <i class="fas fa-angle-right"></i>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ column_rules.paginator.num_pages }}">
                        <i class="fas fa-angle-double-right"></i>
                    </a>
                </li>
            {% endif %}
        </ul>
    </nav>
{% endif %}

<!-- 规则详情模态框 -->
<div class="modal fade" id="ruleDetailModal" tabindex="-1" aria-labelledby="ruleDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="ruleDetailModalLabel">
                    <i class="fas fa-cogs"></i> 栏目规则详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="ruleDetailContent">
                <div class="text-center">
                    <i class="fas fa-spinner fa-spin"></i> 加载中...
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 查看规则详情按钮
    document.querySelectorAll('.view-rule-btn').forEach(function(button) {
        button.addEventListener('click', function() {
            const ruleId = this.getAttribute('data-rule-id');
            const ruleName = this.getAttribute('data-rule-name');
            
            // 更新模态框标题
            document.getElementById('ruleDetailModalLabel').innerHTML = 
                `<i class="fas fa-cogs"></i> ${ruleName} - 规则详情`;
            
            // 加载规则详情
            loadRuleDetail(ruleId);
        });
    });
    
    // 测试规则按钮
    document.querySelectorAll('.test-rule-btn').forEach(function(button) {
        button.addEventListener('click', function() {
            const ruleId = this.getAttribute('data-rule-id');
            // TODO: 实现规则测试功能
            alert('规则测试功能待实现');
        });
    });
});

function loadRuleDetail(ruleId) {
    const contentDiv = document.getElementById('ruleDetailContent');
    contentDiv.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> 加载中...</div>';
    
    // 通过AJAX加载规则详情
    fetch(`/website/api/column_rule_detail/${ruleId}/`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const rule = data.rule;
            contentDiv.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-info-circle text-primary"></i> 基本信息</h6>
                        <table class="table table-sm">
                            <tr><td><strong>规则ID:</strong></td><td>${rule.id}</td></tr>
                            <tr><td><strong>规则名称:</strong></td><td>${rule.name}</td></tr>
                            <tr><td><strong>所属站点:</strong></td><td>${rule.site_name}</td></tr>
                            <tr><td><strong>创建时间:</strong></td><td>${rule.created_at}</td></tr>
                            <tr><td><strong>更新时间:</strong></td><td>${rule.updated_at}</td></tr>
                        </table>
                        
                        <h6><i class="fas fa-chart-bar text-success"></i> 使用统计</h6>
                        <table class="table table-sm">
                            <tr><td><strong>成功次数:</strong></td><td><span class="badge bg-success">${rule.success_count}</span></td></tr>
                            <tr><td><strong>失败次数:</strong></td><td><span class="badge bg-danger">${rule.failed_count}</span></td></tr>
                            <tr><td><strong>测试次数:</strong></td><td><span class="badge bg-info">${rule.test_count}</span></td></tr>
                            <tr><td><strong>使用栏目数:</strong></td><td><span class="badge bg-primary">${rule.columns_using_count}</span></td></tr>
                        </table>
                    </div>
                    
                    <div class="col-md-6">
                        <h6><i class="fas fa-cogs text-warning"></i> 分页配置</h6>
                        <table class="table table-sm">
                            <tr><td><strong>分页类型:</strong></td><td>${rule.pagination_type}</td></tr>
                            <tr><td><strong>下一页选择器:</strong></td><td><code class="small">${rule.next_page_selector}</code></td></tr>
                            <tr><td><strong>最大页数:</strong></td><td>${rule.max_pages}</td></tr>
                        </table>
                        
                        <h6><i class="fas fa-search text-info"></i> 内容提取配置</h6>
                        <table class="table table-sm">
                            <tr><td><strong>容器选择器:</strong></td><td><code class="small">${rule.content_container_selector}</code></td></tr>
                            <tr><td><strong>项目选择器:</strong></td><td><code class="small">${rule.content_item_selector}</code></td></tr>
                            <tr><td><strong>标题选择器:</strong></td><td><code class="small">${rule.title_selector}</code></td></tr>
                            <tr><td><strong>URL选择器:</strong></td><td><code class="small">${rule.url_selector}</code></td></tr>
                            <tr><td><strong>日期选择器:</strong></td><td><code class="small">${rule.date_selector}</code></td></tr>
                        </table>
                    </div>
                </div>
                
                <div class="mt-3">
                    <h6><i class="fas fa-code text-secondary"></i> 完整配置JSON</h6>
                    <details>
                        <summary class="btn btn-outline-secondary btn-sm">点击展开/收起 JSON 配置</summary>
                        <pre class="mt-2 p-3 bg-light border rounded"><code>${JSON.stringify(rule.full_config, null, 2)}</code></pre>
                    </details>
                </div>
            `;
        } else {
            contentDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    加载规则详情失败: ${data.error}
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('加载规则详情失败:', error);
        contentDiv.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                网络请求失败，请稍后重试
            </div>
        `;
    });
}
</script>
{% endblock %}
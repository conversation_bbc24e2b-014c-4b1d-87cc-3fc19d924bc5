{"data_mtime": 1754047861, "dep_lines": [14, 16, 17, 7, 8, 9, 10, 11, 12, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 15], "dep_prios": [5, 5, 5, 10, 10, 5, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["traitlets.config.configurable", "IPython.utils.decorators", "IPython.paths", "atexit", "datetime", "pathlib", "re", "sqlite3", "threading", "traitlets", "builtins", "IPython.utils", "_collections_abc", "_frozen_importlib", "_sqlite3", "_thread", "_typeshed", "abc", "enum", "os", "traitlets.config", "traitlets.traitlets", "traitlets.utils", "traitlets.utils.sentinel", "typing"], "hash": "57b9b469d9e40962f172d1d0281b39bdb34fe22e", "id": "IPython.core.history", "ignore_all": true, "interface_hash": "a6a19f1318220880500f7ed33809bd4370a279db", "mtime": 1722816433, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\IPython\\core\\history.py", "plugin_data": null, "size": 34221, "suppressed": ["decorator"], "version_id": "1.17.1"}
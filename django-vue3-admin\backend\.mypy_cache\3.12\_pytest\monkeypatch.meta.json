{"data_mtime": 1754047858, "dep_lines": [19, 20, 4, 6, 7, 8, 9, 10, 17, 224, 349, 360, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 10, 5, 10, 20, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest.fixtures", "_pytest.warning_types", "__future__", "contextlib", "os", "re", "sys", "typing", "warnings", "inspect", "pkg_resources", "importlib", "builtins", "_collections_abc", "_frozen_importlib", "_pytest.config", "_typeshed", "abc", "enum", "types"], "hash": "124d401da2fe5066fa017317f3a374a6fc03ee09", "id": "_pytest.monkeypatch", "ignore_all": true, "interface_hash": "6d3e3af9acb7fc433b19a440b4e0ce6cd68b50c3", "mtime": 1721810337, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\_pytest\\monkeypatch.py", "plugin_data": null, "size": 14622, "suppressed": [], "version_id": "1.17.1"}
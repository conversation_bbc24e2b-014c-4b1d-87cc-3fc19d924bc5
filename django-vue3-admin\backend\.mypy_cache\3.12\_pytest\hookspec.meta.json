{"data_mtime": 1754047858, "dep_lines": [24, 31, 16, 26, 27, 32, 34, 35, 37, 38, 42, 44, 45, 6, 8, 9, 14, 20, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 5, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 5, 5, 5, 5, 25, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest.config.argparsing", "_pytest.deprecated", "_pytest.compat", "_pytest.config", "_pytest.fixtures", "_pytest.main", "_pytest.nodes", "_pytest.outcomes", "_pytest.python", "_pytest.reports", "_pytest.runner", "_pytest.terminal", "__future__", "pathlib", "typing", "pluggy", "pdb", "warnings", "builtins", "_frozen_importlib", "_pytest._code", "_pytest.warning_types", "abc", "bdb", "cmd", "enum", "os", "pluggy._hooks", "pluggy._manager"], "hash": "90252ac0b41c20b9eeff9052e174603164be67fd", "id": "_pytest.hookspec", "ignore_all": true, "interface_hash": "e6e05b04a71094a9492198ab4c220745a388ff5b", "mtime": 1721810337, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\_pytest\\hookspec.py", "plugin_data": null, "size": 42282, "suppressed": [], "version_id": "1.17.1"}
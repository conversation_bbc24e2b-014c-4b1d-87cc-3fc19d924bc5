{"data_mtime": 1754047860, "dep_lines": [4, 11, 12, 13, 14, 1, 139, 1, 1, 1, 1], "dep_prios": [25, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["rich.console", "rich.jupyter", "rich.measure", "rich.segment", "rich.style", "typing", "rich", "builtins", "_frozen_importlib", "abc", "enum"], "hash": "df179f43dd3b6cab92a13ff8c7abea6cb0b54e49", "id": "rich.padding", "ignore_all": true, "interface_hash": "0d5c47163038ba8d9c540a3fe3651dbf7e5a5a7f", "mtime": 1747034635, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\rich\\padding.py", "plugin_data": null, "size": 4896, "suppressed": [], "version_id": "1.17.1"}
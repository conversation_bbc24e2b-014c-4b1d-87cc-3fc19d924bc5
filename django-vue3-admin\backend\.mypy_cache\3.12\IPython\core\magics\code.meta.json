{"data_mtime": 1754047861, "dep_lines": [28, 29, 30, 31, 32, 33, 34, 35, 38, 23, 24, 16, 17, 18, 19, 20, 21, 22, 25, 36, 37, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["IPython.core.error", "IPython.core.macro", "IPython.core.magic", "IPython.core.oinspect", "IPython.core.release", "IPython.testing.skipdoctest", "IPython.utils.contexts", "IPython.utils.path", "IPython.utils.text", "urllib.request", "urllib.parse", "inspect", "io", "os", "re", "sys", "ast", "itertools", "pathlib", "warnings", "logging", "builtins", "IPython.testing", "_frozen_importlib", "abc", "enum", "traitlets", "traitlets.config", "traitlets.config.configurable", "traitlets.traitlets", "typing"], "hash": "56bd319b77a62e2bd21c1aa4e881e9b07fce4145", "id": "IPython.core.magics.code", "ignore_all": true, "interface_hash": "dc95683f28796b85ffc1f914267862b70494bce9", "mtime": 1722816433, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\IPython\\core\\magics\\code.py", "plugin_data": null, "size": 28051, "suppressed": [], "version_id": "1.17.1"}
{"data_mtime": 1754047862, "dep_lines": [1, 2, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["httpx", "json", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_ssl", "abc", "http", "http.cookiejar", "httpx._api", "httpx._auth", "httpx._config", "httpx._exceptions", "httpx._models", "httpx._urls", "json.decoder", "ssl"], "hash": "ec4671985df2ee3a2937788aea895ec97d006190", "id": "apps.website.service.ai_services.coze_service", "ignore_all": false, "interface_hash": "a8a64852aa10b696333fb25af5d5fc551f1389c8", "mtime": 1750917979, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "D:\\Project\\node\\crawl_all\\django-vue3-admin\\backend\\apps\\website\\service\\ai_services\\coze_service.py", "plugin_data": null, "size": 3336, "suppressed": [], "version_id": "1.17.1"}
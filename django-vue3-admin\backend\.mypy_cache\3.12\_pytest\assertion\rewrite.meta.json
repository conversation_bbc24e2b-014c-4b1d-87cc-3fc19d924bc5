{"data_mtime": 1754047858, "dep_lines": [29, 32, 284, 292, 9, 10, 11, 31, 32, 33, 34, 35, 37, 268, 290, 3, 5, 6, 7, 8, 9, 12, 13, 14, 15, 16, 18, 19, 20, 21, 22, 862, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 20, 20, 10, 10, 10, 5, 20, 5, 5, 5, 5, 20, 20, 5, 10, 5, 10, 10, 20, 10, 10, 10, 10, 5, 10, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest._io.saferepr", "_pytest.assertion.util", "importlib.resources.abc", "importlib.resources.readers", "importlib.abc", "importlib.machinery", "importlib.util", "_pytest._version", "_pytest.assertion", "_pytest.config", "_pytest.main", "_pytest.pathlib", "_pytest.stash", "_pytest.warning_types", "importlib.readers", "__future__", "ast", "collections", "errno", "functools", "importlib", "io", "itertools", "marshal", "os", "pathlib", "struct", "sys", "tokenize", "types", "typing", "warnings", "builtins", "_collections_abc", "_frozen_importlib", "_frozen_importlib_external", "_pytest.nodes", "_typeshed", "_warnings", "abc", "importlib._abc", "importlib.resources", "pluggy", "pluggy._tracing", "posixpath"], "hash": "349eb95575cb10c5a9c728cd7f1481f5497dfcd6", "id": "_pytest.assertion.rewrite", "ignore_all": true, "interface_hash": "5fe5ad7e823f262e39031bdeea08246b93a4c3e2", "mtime": 1721810337, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\_pytest\\assertion\\rewrite.py", "plugin_data": null, "size": 47293, "suppressed": [], "version_id": "1.17.1"}
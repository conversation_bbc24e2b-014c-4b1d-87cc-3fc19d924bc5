{"data_mtime": 1754047861, "dep_lines": [37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 49, 50, 37, 40, 16, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27, 29, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 53, 54, 52], "dep_prios": [10, 5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5], "dependencies": ["IPython.core.page", "IPython.lib.pretty", "IPython.testing.skipdoctest", "IPython.utils.PyColorize", "IPython.utils.openpy", "IPython.utils.dir2", "IPython.utils.path", "IPython.utils.text", "IPython.utils.wildcard", "IPython.utils.coloransi", "IPython.utils.py3compat", "IPython.utils.colorable", "IPython.utils.decorators", "IPython.core", "IPython.utils", "dataclasses", "inspect", "textwrap", "ast", "html", "io", "linecache", "os", "sys", "types", "warnings", "typing", "builtins", "IPython.testing", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "traitlets", "traitlets.config", "traitlets.config.configurable", "traitlets.traitlets"], "hash": "5cdbab68d9c6697eb7dcae9be5ad225de0bf163b", "id": "IPython.core.oinspect", "ignore_all": true, "interface_hash": "006609b4bf4c9002389e384f949bdd5dde5c42fe", "mtime": 1722816433, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\IPython\\core\\oinspect.py", "plugin_data": null, "size": 39844, "suppressed": ["pygments.lexers", "pygments.formatters", "pygments"], "version_id": "1.17.1"}
{"data_mtime": 1754047861, "dep_lines": [29, 29, 29, 30, 31, 32, 43, 44, 45, 46, 47, 48, 49, 50, 29, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 24, 25, 26, 27, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["IPython.core.magic_arguments", "IPython.core.oinspect", "IPython.core.page", "IPython.core.error", "IPython.core.macro", "IPython.core.magic", "IPython.testing.skipdoctest", "IPython.utils.capture", "IPython.utils.contexts", "IPython.utils.ipstruct", "IPython.utils.module_paths", "IPython.utils.path", "IPython.utils.timing", "IPython.core.displayhook", "IPython.core", "ast", "bdb", "builtins", "cProfile", "gc", "itertools", "math", "os", "pstats", "re", "shlex", "sys", "time", "timeit", "io", "logging", "pathlib", "pdb", "warnings", "IPython.testing", "IPython.utils", "_collections_abc", "_frozen_importlib", "_io", "_lsprof", "_typeshed", "_typeshed.importlib", "_warnings", "abc", "enum", "posixpath", "profile", "traitlets", "traitlets.config", "traitlets.config.configurable", "traitlets.traitlets", "types", "typing"], "hash": "0653c033f1c13bc3929fb5bf3c247f43c07f4594", "id": "IPython.core.magics.execution", "ignore_all": true, "interface_hash": "72746927f10e7e700a79fa57f101c01e927aa7d5", "mtime": 1722816433, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\IPython\\core\\magics\\execution.py", "plugin_data": null, "size": 58332, "suppressed": [], "version_id": "1.17.1"}
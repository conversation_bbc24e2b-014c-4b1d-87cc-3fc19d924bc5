{"data_mtime": 1754047860, "dep_lines": [6, 8, 9, 10, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3], "dep_prios": [5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["IPython.core.displayhook", "prompt_toolkit.formatted_text", "prompt_toolkit.shortcuts", "prompt_toolkit.enums", "sys", "builtins", "IPython.core", "_frozen_importlib", "abc", "enum", "prompt_toolkit", "prompt_toolkit.formatted_text.pygments", "prompt_toolkit.output", "prompt_toolkit.output.base", "prompt_toolkit.output.color_depth", "prompt_toolkit.shortcuts.utils", "prompt_toolkit.styles", "prompt_toolkit.styles.base", "prompt_toolkit.styles.style_transformation", "traitlets", "traitlets.config", "traitlets.config.configurable", "traitlets.traitlets", "typing"], "hash": "743130994329c787994995ce65b3522be798558b", "id": "IPython.terminal.prompts", "ignore_all": true, "interface_hash": "d8afde747dfc901e0c096afa11c02b36e41440a1", "mtime": 1722816433, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\IPython\\terminal\\prompts.py", "plugin_data": null, "size": 3360, "suppressed": ["pygments.token"], "version_id": "1.17.1"}
{"data_mtime": 1754047861, "dep_lines": [11, 12, 12, 13, 14, 15, 16, 17, 400, 12, 582, 4, 5, 6, 7, 8, 9, 582, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 5, 5, 5, 20, 20, 20, 5, 10, 10, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["traitlets.utils.importstring", "IPython.core.magic_arguments", "IPython.core.page", "IPython.core.error", "IPython.core.magic", "IPython.utils.text", "IPython.testing.skipdoctest", "IPython.utils.ipstruct", "IPython.core.usage", "IPython.core", "nbformat.v4", "logging", "io", "os", "pprint", "sys", "warnings", "nbformat", "builtins", "IPython.testing", "IPython.utils", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "traitlets", "traitlets.config", "traitlets.config.configurable", "traitlets.traitlets", "traitlets.utils", "typing"], "hash": "55ba00db5bf80419165d39affadb766c7cb68802", "id": "IPython.core.magics.basic", "ignore_all": true, "interface_hash": "aefc2a5acadd4a27fa9a920b2e2bfa4fff5461ff", "mtime": 1722816433, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\IPython\\core\\magics\\basic.py", "plugin_data": null, "size": 23070, "suppressed": [], "version_id": "1.17.1"}
{"data_mtime": 1754047859, "dep_lines": [7, 15, 16, 17, 22, 23, 5, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["prompt_toolkit.styles.base", "prompt_toolkit.styles.defaults", "prompt_toolkit.styles.named_colors", "prompt_toolkit.styles.pygments", "prompt_toolkit.styles.style", "prompt_toolkit.styles.style_transformation", "__future__", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "1d653f870c1e566d91da5e790665c38a446ca46d", "id": "prompt_toolkit.styles", "ignore_all": true, "interface_hash": "5b3bd87f3352f3753977a804e7c6de8a663b49df", "mtime": 1722816426, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\prompt_toolkit\\styles\\__init__.py", "plugin_data": null, "size": 1640, "suppressed": [], "version_id": "1.17.1"}
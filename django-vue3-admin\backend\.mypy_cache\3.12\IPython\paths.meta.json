{"data_mtime": 1754047861, "dep_lines": [8, 9, 3, 3, 4, 5, 7, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 20, 10, 5, 10, 5, 30, 30, 30], "dependencies": ["IPython.utils.importstring", "IPython.utils.path", "os.path", "os", "tempfile", "warnings", "IPython", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "b746d559d6467542da6c038aa6651b0874df9261", "id": "IPython.paths", "ignore_all": true, "interface_hash": "4444e65f43100ca6211225e9cd15a72f26daf119", "mtime": 1722816433, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\IPython\\paths.py", "plugin_data": null, "size": 4335, "suppressed": [], "version_id": "1.17.1"}
{"data_mtime": 1754047860, "dep_lines": [1, 2, 3, 4, 5, 8, 20, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["httpcore._async.connection", "httpcore._async.connection_pool", "httpcore._async.http11", "httpcore._async.http_proxy", "httpcore._async.interfaces", "httpcore._async.http2", "httpcore._async.socks_proxy", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "b229f232e43d292ba57d309b82fed0e0ee362493", "id": "httpcore._async", "ignore_all": true, "interface_hash": "e28a5c4b342d62cea84eca8fe0969d7d34524bb7", "mtime": 1743038711, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\httpcore\\_async\\__init__.py", "plugin_data": null, "size": 1221, "suppressed": [], "version_id": "1.17.1"}
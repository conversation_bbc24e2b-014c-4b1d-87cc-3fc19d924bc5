{"data_mtime": 1754047861, "dep_lines": [56, 57, 59, 60, 60, 60, 60, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 79, 80, 83, 84, 84, 84, 84, 85, 87, 88, 89, 90, 91, 92, 133, 2203, 2204, 2306, 2616, 3590, 59, 81, 82, 84, 3802, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 41, 59, 893, 2924, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 99, 3588, 39, 99], "dep_prios": [5, 5, 10, 10, 5, 10, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 5, 5, 20, 20, 10, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 10, 10, 5, 5, 5, 5, 5, 5, 20, 20, 20, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 20, 5, 20], "dependencies": ["traitlets.config.configurable", "traitlets.utils.importstring", "IPython.core.hooks", "IPython.core.magic", "IPython.core.oinspect", "IPython.core.page", "IPython.core.prefilter", "IPython.core.ultratb", "IPython.core.alias", "IPython.core.autocall", "IPython.core.builtin_trap", "IPython.core.compilerop", "IPython.core.debugger", "IPython.core.display_trap", "IPython.core.displayhook", "IPython.core.displaypub", "IPython.core.error", "IPython.core.events", "IPython.core.extensions", "IPython.core.formatters", "IPython.core.history", "IPython.core.inputtransformer2", "IPython.core.logger", "IPython.core.macro", "IPython.core.payload", "IPython.core.profiledir", "IPython.core.usage", "IPython.testing.skipdoctest", "IPython.utils.PyColorize", "IPython.utils.io", "IPython.utils.openpy", "IPython.utils.py3compat", "IPython.utils.decorators", "IPython.utils.ipstruct", "IPython.utils.path", "IPython.utils.process", "IPython.utils.strdispatch", "IPython.utils.syspathcontext", "IPython.utils.text", "IPython.core.async_helpers", "IPython.core.completer", "IPython.core.completerlib", "IPython.core.magics", "IPython.utils._process_win32", "IPython.core.pylabtools", "IPython.core", "IPython.display", "IPython.paths", "IPython.utils", "urllib.request", "abc", "ast", "atexit", "bdb", "builtins", "functools", "inspect", "os", "re", "runpy", "subprocess", "sys", "tempfile", "traceback", "types", "warnings", "io", "logging", "pathlib", "typing", "traitlets", "IPython", "site", "nbformat", "IPython.core.display_functions", "IPython.core.magics.auto", "IPython.core.magics.basic", "IPython.core.magics.code", "IPython.core.magics.config", "IPython.core.magics.display", "IPython.core.magics.execution", "IPython.core.magics.extension", "IPython.core.magics.history", "IPython.core.magics.logging", "IPython.core.magics.namespace", "IPython.core.magics.osm", "IPython.core.magics.packaging", "IPython.core.magics.pylab", "IPython.core.magics.script", "IPython.testing", "IPython.utils.colorable", "IPython.utils.coloransi", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "_warnings", "cmd", "codeop", "enum", "ntpath", "pdb", "posixpath", "string", "traitlets.config", "traitlets.traitlets", "traitlets.utils", "traitlets.utils.sentinel"], "hash": "abe6d5c8f6c76d073c366fc1dcf574c67200ce83", "id": "IPython.core.interactiveshell", "ignore_all": true, "interface_hash": "7704f661c0a29f28d86840f4c9f751c3ce5e9cbd", "mtime": 1722816433, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\IPython\\core\\interactiveshell.py", "plugin_data": null, "size": 152390, "suppressed": ["docrepr.sphinxify", "matplotlib_inline.backend_inline", "pickleshare", "docrepr"], "version_id": "1.17.1"}
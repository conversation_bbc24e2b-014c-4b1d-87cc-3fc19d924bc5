{"data_mtime": 1754047861, "dep_lines": [21, 22, 41, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 59, 60, 61, 64, 65, 67, 71, 73, 74, 75, 81, 82, 83, 16, 17, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["playwright._impl._accessibility", "playwright._impl._api_structures", "playwright._impl._assertions", "playwright._impl._browser", "playwright._impl._browser_context", "playwright._impl._browser_type", "playwright._impl._cdp_session", "playwright._impl._clock", "playwright._impl._console_message", "playwright._impl._dialog", "playwright._impl._download", "playwright._impl._element_handle", "playwright._impl._errors", "playwright._impl._fetch", "playwright._impl._file_chooser", "playwright._impl._frame", "playwright._impl._input", "playwright._impl._js_handle", "playwright._impl._locator", "playwright._impl._network", "playwright._impl._page", "playwright._impl._playwright", "playwright._impl._selectors", "playwright._impl._sync_base", "playwright._impl._tracing", "playwright._impl._video", "playwright._impl._web_error", "datetime", "pathlib", "typing", "builtins", "_frozen_importlib", "abc", "asyncio", "asyncio.events", "contextlib", "os", "playwright._impl", "playwright._impl._artifact", "playwright._impl._connection", "playwright._impl._impl_to_api_mapping", "pyee", "pyee.asyncio", "pyee.base", "re"], "hash": "7053a208a42ea30678896f422c8123101d8a8b7f", "id": "playwright.sync_api._generated", "ignore_all": true, "interface_hash": "60c6b716db62b5d1ca9b8de9f86d79fe944a709e", "mtime": 1722410462, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\playwright\\sync_api\\_generated.py", "plugin_data": null, "size": 813308, "suppressed": [], "version_id": "1.17.1"}
{"data_mtime": 1754047858, "dep_lines": [37, 40, 53, 62, 33, 34, 35, 41, 51, 54, 59, 60, 66, 68, 72, 74, 75, 4, 6, 7, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 30, 32, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 10, 10, 5, 10, 10, 10, 5, 10, 5, 10, 10, 5, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest._io.saferepr", "_pytest.config.argparsing", "_pytest.mark.structures", "_pytest.fixtures", "_pytest.nodes", "_pytest._code", "_pytest.compat", "_pytest.config", "_pytest.deprecated", "_pytest.main", "_pytest.mark", "_pytest.outcomes", "_pytest.pathlib", "_pytest.scope", "_pytest.stash", "_pytest.warning_types", "__future__", "abc", "collections", "dataclasses", "enum", "fnmatch", "functools", "inspect", "itertools", "os", "pathlib", "types", "typing", "warnings", "_pytest", "builtins", "_frozen_importlib", "_typeshed", "pluggy", "pluggy._hooks"], "hash": "3ec2a2f49da4238470210f61a045e8daf44914c5", "id": "_pytest.python", "ignore_all": true, "interface_hash": "d07f98512dc5154beb4a295b6b3e92fc77f10049", "mtime": 1721810337, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\_pytest\\python.py", "plugin_data": null, "size": 64893, "suppressed": [], "version_id": "1.17.1"}